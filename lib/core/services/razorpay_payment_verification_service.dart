import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import '../../config/environment/environment.dart';
import './auth_token_manager.dart';
import '../../utils/storage/sso_storage.dart';
import '../utils/transaction_status_handler.dart';

/// Payment verification service following SSO-Flutter patterns
/// Handles server-side payment verification, status checking, and transaction validation
class RazorpayPaymentVerificationService {
  static const String _logName = 'RAZORPAY_VERIFICATION';

  /// Verify payment status following SSO-Flutter pattern
  /// Similar to: sso-flutter/lib/ui/module/chsone/payment/onlinepaid/pay_online_stepper.dart
  static Future<Map<String, dynamic>> verifyPaymentStatus({
    required String orderId,
    required String paymentId,
    String? billType,
  }) async {
    try {
      if (kDebugMode) {
        log('🔍 [RAZORPAY_VERIFICATION] Verifying payment status for order: $orderId');
      }

      // Get authentication headers following SSO-Flutter pattern
      final headers = await _getAuthHeaders();
      
      // Construct verification URL following SSO-Flutter pattern
      final url = '${Environment.config.chsoneOperatorsUrl}getOrderStatus'
          '?order_id=$orderId'
          '&payment_id=$paymentId'
          '${billType != null ? '&bill_type=$billType' : ''}';

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        
        // Process transaction status using SSO-Flutter pattern
        final status = TransactionStatusHandler.getTransactionStatus(result);
        
        if (kDebugMode) {
          log('✅ [RAZORPAY_VERIFICATION] Payment verification completed: $status');
        }
        
        return {
          'status': status.toString().split('.').last,
          'raw_response': result,
          'order_id': orderId,
          'payment_id': paymentId,
          'verified_at': DateTime.now().toIso8601String(),
        };
      } else {
        throw Exception('Payment verification failed: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Payment verification error: $e');
      }
      rethrow;
    }
  }

  /// Verify Razorpay webhook signature following security best practices
  /// Implements HMAC-SHA256 verification as per Razorpay documentation
  static bool verifyWebhookSignature({
    required String payload,
    required String signature,
    required String webhookSecret,
  }) {
    try {
      if (kDebugMode) {
        log('🔐 [RAZORPAY_VERIFICATION] Verifying webhook signature');
      }

      // Remove 'sha256=' prefix if present
      final cleanSignature = signature.startsWith('sha256=') 
          ? signature.substring(7) 
          : signature;

      // Generate HMAC-SHA256 signature
      final key = utf8.encode(webhookSecret);
      final bytes = utf8.encode(payload);
      final hmacSha256 = Hmac(sha256, key);
      final digest = hmacSha256.convert(bytes);
      final generatedSignature = digest.toString();

      final isValid = generatedSignature == cleanSignature;
      
      if (kDebugMode) {
        log(isValid 
            ? '✅ [RAZORPAY_VERIFICATION] Webhook signature valid'
            : '❌ [RAZORPAY_VERIFICATION] Webhook signature invalid');
      }
      
      return isValid;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Webhook signature verification error: $e');
      }
      return false;
    }
  }

  /// Verify payment signature following Razorpay security guidelines
  /// Server-side verification for payment completion
  static Future<bool> verifyPaymentSignatureServerSide({
    required String paymentId,
    required String orderId,
    required String signature,
  }) async {
    try {
      if (kDebugMode) {
        log('🔐 [RAZORPAY_VERIFICATION] Server-side signature verification');
      }

      // Get authentication headers
      final headers = await _getAuthHeaders();
      
      // Send verification request to backend
      final response = await http.post(
        Uri.parse('${Environment.config.chsoneOperatorsUrl}verifyPaymentSignature'),
        headers: headers,
        body: json.encode({
          'razorpay_payment_id': paymentId,
          'razorpay_order_id': orderId,
          'razorpay_signature': signature,
          'verification_source': 'oneapp_flutter',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        return result['signature_valid'] == true;
      } else {
        if (kDebugMode) {
          log('❌ [RAZORPAY_VERIFICATION] Server verification failed: ${response.statusCode}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Server verification error: $e');
      }
      return false;
    }
  }

  /// Client-side signature verification (for development/fallback)
  /// Note: Production should always use server-side verification
  static bool verifyPaymentSignatureClientSide({
    required String paymentId,
    required String orderId,
    required String signature,
    required String keySecret,
  }) {
    try {
      if (kDebugMode) {
        log('⚠️ [RAZORPAY_VERIFICATION] Client-side signature verification (development only)');
      }

      // Create payload for signature verification
      final payload = '$orderId|$paymentId';
      
      // Generate HMAC-SHA256 signature
      final key = utf8.encode(keySecret);
      final bytes = utf8.encode(payload);
      final hmacSha256 = Hmac(sha256, key);
      final digest = hmacSha256.convert(bytes);
      final generatedSignature = digest.toString();

      return generatedSignature == signature;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Client-side verification error: $e');
      }
      return false;
    }
  }

  /// Get payment details from Razorpay API
  /// Following SSO-Flutter pattern for external API calls
  static Future<Map<String, dynamic>> getPaymentDetails({
    required String paymentId,
  }) async {
    try {
      if (kDebugMode) {
        log('📋 [RAZORPAY_VERIFICATION] Fetching payment details for: $paymentId');
      }

      // Get authentication headers
      final headers = await _getAuthHeaders();
      
      // Fetch payment details through backend proxy
      final response = await http.get(
        Uri.parse('${Environment.config.chsoneOperatorsUrl}getPaymentDetails?payment_id=$paymentId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        
        if (kDebugMode) {
          log('✅ [RAZORPAY_VERIFICATION] Payment details retrieved');
        }
        
        return result;
      } else {
        throw Exception('Failed to fetch payment details: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Failed to get payment details: $e');
      }
      rethrow;
    }
  }

  /// Comprehensive payment validation following SSO-Flutter patterns
  /// Combines multiple verification steps for robust validation
  static Future<Map<String, dynamic>> validatePayment({
    required String paymentId,
    required String orderId,
    required String signature,
    String? billType,
  }) async {
    try {
      if (kDebugMode) {
        log('🔍 [RAZORPAY_VERIFICATION] Starting comprehensive payment validation');
      }

      final validationResults = <String, dynamic>{
        'payment_id': paymentId,
        'order_id': orderId,
        'validation_timestamp': DateTime.now().toIso8601String(),
        'validation_steps': <String, dynamic>{},
      };

      // Step 1: Verify payment signature
      try {
        final signatureValid = await verifyPaymentSignatureServerSide(
          paymentId: paymentId,
          orderId: orderId,
          signature: signature,
        );
        validationResults['validation_steps']['signature_verification'] = {
          'status': signatureValid ? 'passed' : 'failed',
          'timestamp': DateTime.now().toIso8601String(),
        };
      } catch (e) {
        validationResults['validation_steps']['signature_verification'] = {
          'status': 'error',
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      // Step 2: Verify payment status
      try {
        final statusResult = await verifyPaymentStatus(
          orderId: orderId,
          paymentId: paymentId,
          billType: billType,
        );
        validationResults['validation_steps']['status_verification'] = {
          'status': 'completed',
          'payment_status': statusResult['status'],
          'timestamp': DateTime.now().toIso8601String(),
        };
      } catch (e) {
        validationResults['validation_steps']['status_verification'] = {
          'status': 'error',
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      // Step 3: Get payment details
      try {
        final paymentDetails = await getPaymentDetails(paymentId: paymentId);
        validationResults['validation_steps']['details_retrieval'] = {
          'status': 'completed',
          'timestamp': DateTime.now().toIso8601String(),
        };
        validationResults['payment_details'] = paymentDetails;
      } catch (e) {
        validationResults['validation_steps']['details_retrieval'] = {
          'status': 'error',
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      // Determine overall validation result
      final allStepsPassed = validationResults['validation_steps'].values
          .every((step) => step['status'] == 'passed' || step['status'] == 'completed');
      
      validationResults['overall_status'] = allStepsPassed ? 'valid' : 'invalid';
      
      if (kDebugMode) {
        log('✅ [RAZORPAY_VERIFICATION] Payment validation completed: ${validationResults['overall_status']}');
      }
      
      return validationResults;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Payment validation error: $e');
      }
      rethrow;
    }
  }

  /// Get authentication headers following SSO-Flutter pattern
  static Future<Map<String, String>> _getAuthHeaders() async {
    try {
      final accessToken = await AuthTokenManager.getBestAvailableToken();
      final userProfile = await SsoStorage.getUserProfile();
      final apiToken = userProfile?['api_token'];
      
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-MClient': '14',
      };
      
      if (accessToken != null) {
        headers['x-access-token'] = accessToken;
      }
      
      if (apiToken != null) {
        headers['x-api-token'] = apiToken;
      }
      
      return headers;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_VERIFICATION] Failed to get auth headers: $e');
      }
      
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    }
  }
}
