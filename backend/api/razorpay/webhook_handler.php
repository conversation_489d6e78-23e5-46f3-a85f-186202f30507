<?php
/**
 * Razorpay Webhook Handler
 * Following SSO-Flutter security patterns for webhook processing
 * 
 * Endpoint: POST /api/v1/webhook/razorpay/payment-success
 * Headers: x-access-token, x-api-token, X-MClient
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, x-access-token, x-api-token, X-MClient');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

require_once '../config/database.php';
require_once '../config/razorpay_config.php';
require_once '../utils/auth_validator.php';
require_once '../utils/logger.php';
require_once '../utils/webhook_verifier.php';
require_once '../utils/notification_service.php';

try {
    // Validate authentication headers (SSO-Flutter pattern)
    $auth_result = AuthValidator::validateRequest();
    if (!$auth_result['valid']) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication failed', 'details' => $auth_result['message']]);
        exit();
    }

    // Get request body
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required_fields = ['payment_id', 'order_id', 'signature', 'payment_data'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit();
        }
    }

    $payment_id = $input['payment_id'];
    $order_id = $input['order_id'];
    $signature = $input['signature'];
    $payment_data = $input['payment_data'];
    $webhook_verified = $input['webhook_verified'] ?? false;

    // Verify webhook signature if provided
    if ($webhook_verified && isset($input['webhook_payload']) && isset($input['webhook_signature'])) {
        $webhook_valid = WebhookVerifier::verifyWebhookSignature(
            $input['webhook_payload'],
            $input['webhook_signature'],
            RAZORPAY_WEBHOOK_SECRET
        );
        
        if (!$webhook_valid) {
            Logger::log('WARNING', 'Invalid webhook signature', [
                'payment_id' => $payment_id,
                'order_id' => $order_id
            ]);
            
            http_response_code(400);
            echo json_encode(['error' => 'Invalid webhook signature']);
            exit();
        }
    }

    // Verify payment signature
    $signature_valid = WebhookVerifier::verifyPaymentSignature(
        $payment_id,
        $order_id,
        $signature,
        RAZORPAY_KEY_SECRET
    );

    if (!$signature_valid) {
        Logger::log('WARNING', 'Invalid payment signature in webhook', [
            'payment_id' => $payment_id,
            'order_id' => $order_id
        ]);
        
        http_response_code(400);
        echo json_encode(['error' => 'Invalid payment signature']);
        exit();
    }

    // Check if order exists
    $stmt = $pdo->prepare("SELECT * FROM razorpay_orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        Logger::log('WARNING', 'Webhook received for unknown order', [
            'payment_id' => $payment_id,
            'order_id' => $order_id
        ]);
        
        http_response_code(404);
        echo json_encode(['error' => 'Order not found']);
        exit();
    }

    // Check if payment already processed
    $stmt = $pdo->prepare("SELECT * FROM payment_webhooks WHERE payment_id = ?");
    $stmt->execute([$payment_id]);
    $existing_webhook = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_webhook) {
        Logger::log('INFO', 'Duplicate webhook received', [
            'payment_id' => $payment_id,
            'order_id' => $order_id
        ]);
        
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Payment already processed',
            'payment_id' => $payment_id
        ]);
        exit();
    }

    // Begin transaction
    $pdo->beginTransaction();

    try {
        // Store webhook data
        $stmt = $pdo->prepare("
            INSERT INTO payment_webhooks 
            (payment_id, order_id, signature, payment_data, webhook_verified, processed_by, user_id, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $payment_id,
            $order_id,
            $signature,
            json_encode($payment_data),
            $webhook_verified ? 1 : 0,
            $input['processed_by'] ?? 'oneapp_flutter',
            $auth_result['user_id']
        ]);

        // Update order status
        $stmt = $pdo->prepare("
            UPDATE razorpay_orders 
            SET status = 'paid', payment_id = ?, webhook_processed_at = NOW() 
            WHERE order_id = ?
        ");
        $stmt->execute([$payment_id, $order_id]);

        // Update related maintenance payment if exists
        $stmt = $pdo->prepare("
            UPDATE maintenance_payments 
            SET payment_status = 'completed', razorpay_payment_id = ?, completed_at = NOW() 
            WHERE order_id = ?
        ");
        $stmt->execute([$payment_id, $order_id]);

        // Commit transaction
        $pdo->commit();

        // Send success notification
        NotificationService::sendPaymentSuccessNotification([
            'user_id' => $auth_result['user_id'],
            'payment_id' => $payment_id,
            'order_id' => $order_id,
            'amount' => $order['amount'],
            'payment_method' => $payment_data['method'] ?? 'unknown'
        ]);

        // Log successful processing
        Logger::log('INFO', 'Payment webhook processed successfully', [
            'payment_id' => $payment_id,
            'order_id' => $order_id,
            'amount' => $order['amount'],
            'user_id' => $auth_result['user_id']
        ]);

        // Return success response
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Payment processed successfully',
            'payment_id' => $payment_id,
            'order_id' => $order_id,
            'processed_at' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        // Rollback transaction
        $pdo->rollback();
        throw $e;
    }

} catch (Exception $e) {
    Logger::log('ERROR', 'Webhook processing failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'payment_id' => $input['payment_id'] ?? 'unknown',
        'order_id' => $input['order_id'] ?? 'unknown',
        'user_id' => $auth_result['user_id'] ?? 'unknown'
    ]);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Webhook processing failed',
        'details' => 'Internal server error'
    ]);
}
?>
