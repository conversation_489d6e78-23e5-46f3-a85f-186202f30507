import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../config/environment/environment.dart';
import './auth_token_manager.dart';
import '../../utils/storage/sso_storage.dart';
import './razorpay_payment_verification_service.dart';

/// Razorpay webhook handler following SSO-Flutter security patterns
/// Handles webhook events from Razorpay with proper authentication and verification
class RazorpayWebhookHandler {
  static const String _logName = 'RAZORPAY_WEBHOOK';

  /// Handle payment success webhook following SSO-Flutter patterns
  static Future<Map<String, dynamic>> handlePaymentSuccess({
    required String paymentId,
    required String orderId,
    required String signature,
    required Map<String, dynamic> paymentData,
    String? webhookPayload,
    String? webhookSignature,
  }) async {
    try {
      if (kDebugMode) {
        log('🎯 [$_logName] Handling payment success webhook');
        log('   - Payment ID: $paymentId');
        log('   - Order ID: $orderId');
      }

      // Step 1: Verify webhook signature if provided (SSO-Flutter security pattern)
      if (webhookPayload != null && webhookSignature != null) {
        final webhookSecret = Environment.config.razorpayWebhookSecret;
        final isValidWebhook = RazorpayPaymentVerificationService.verifyWebhookSignature(
          payload: webhookPayload,
          signature: webhookSignature,
          webhookSecret: webhookSecret,
        );

        if (!isValidWebhook) {
          throw Exception('Invalid webhook signature');
        }
      }

      // Step 2: Verify payment signature (SSO-Flutter verification pattern)
      final isValidPayment = await RazorpayPaymentVerificationService.verifyPaymentSignatureServerSide(
        paymentId: paymentId,
        orderId: orderId,
        signature: signature,
      );

      if (!isValidPayment) {
        throw Exception('Invalid payment signature');
      }

      // Step 3: Get authentication headers following SSO-Flutter pattern
      final headers = await _getAuthHeaders();

      // Step 4: Process payment success through CHSOne backend
      final response = await http.post(
        Uri.parse('${Environment.config.chsoneOperatorsUrl}webhook/razorpay/payment-success'),
        headers: headers,
        body: json.encode({
          'payment_id': paymentId,
          'order_id': orderId,
          'signature': signature,
          'payment_data': paymentData,
          'webhook_verified': webhookPayload != null,
          'processed_by': 'oneapp_flutter',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);

        if (kDebugMode) {
          log('✅ [$_logName] Payment success processed successfully');
        }

        return result;
      } else {
        throw Exception('Webhook processing failed: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [$_logName] Payment success handling failed: $e');
      }
      rethrow;
    }
  }

  /// Handle payment failure webhook
  static Future<Map<String, dynamic>> handlePaymentFailure({
    required String orderId,
    required Map<String, dynamic> errorData,
  }) async {
    try {
      if (kDebugMode) {
        log('💥 [RAZORPAY_WEBHOOK] Handling payment failure webhook');
        log('   - Order ID: $orderId');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/webhook/razorpay/payment-failure'),
        headers: {
          'Content-Type': 'application/json',
          'x-api-token': Environment.config.apiToken,
        },
        body: json.encode({
          'order_id': orderId,
          'error_data': errorData,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        
        if (kDebugMode) {
          log('✅ [RAZORPAY_WEBHOOK] Payment failure processed');
        }
        
        return result;
      } else {
        throw Exception('Webhook processing failed: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_WEBHOOK] Payment failure handling failed: $e');
      }
      rethrow;
    }
  }

  /// Handle order creation webhook
  static Future<Map<String, dynamic>> handleOrderCreated({
    required String orderId,
    required double amount,
    required Map<String, dynamic> orderData,
  }) async {
    try {
      if (kDebugMode) {
        log('📝 [RAZORPAY_WEBHOOK] Handling order creation webhook');
        log('   - Order ID: $orderId');
        log('   - Amount: $amount');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/webhook/razorpay/order-created'),
        headers: {
          'Content-Type': 'application/json',
          'x-api-token': Environment.config.apiToken,
        },
        body: json.encode({
          'order_id': orderId,
          'amount': amount,
          'order_data': orderData,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        
        if (kDebugMode) {
          log('✅ [RAZORPAY_WEBHOOK] Order creation processed');
        }
        
        return result;
      } else {
        throw Exception('Webhook processing failed: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_WEBHOOK] Order creation handling failed: $e');
      }
      rethrow;
    }
  }

  /// Verify webhook signature for security
  static Future<bool> _verifyWebhookSignature({
    required String paymentId,
    required String orderId,
    required String signature,
  }) async {
    try {
      // TODO: Implement proper signature verification
      // This should use HMAC-SHA256 with your webhook secret
      
      // For now, return true (implement proper verification in production)
      if (kDebugMode) {
        log('🔐 [RAZORPAY_WEBHOOK] Verifying signature (mock implementation)');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_WEBHOOK] Signature verification failed: $e');
      }
      return false;
    }
  }

  /// Get webhook event type from payload
  static String getEventType(Map<String, dynamic> payload) {
    return payload['event'] ?? 'unknown';
  }

  /// Validate webhook payload structure
  static bool isValidPayload(Map<String, dynamic> payload) {
    try {
      // Check required fields
      final requiredFields = ['event', 'account_id', 'created_at'];
      
      for (final field in requiredFields) {
        if (!payload.containsKey(field)) {
          if (kDebugMode) {
            log('❌ [RAZORPAY_WEBHOOK] Missing required field: $field');
          }
          return false;
        }
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_WEBHOOK] Payload validation failed: $e');
      }
      return false;
    }
  }

  /// Process webhook based on event type
  static Future<Map<String, dynamic>> processWebhook(Map<String, dynamic> payload) async {
    try {
      if (!isValidPayload(payload)) {
        throw Exception('Invalid webhook payload');
      }

      final eventType = getEventType(payload);
      
      if (kDebugMode) {
        log('🎯 [RAZORPAY_WEBHOOK] Processing webhook event: $eventType');
      }

      switch (eventType) {
        case 'payment.captured':
          return await handlePaymentSuccess(
            paymentId: payload['payload']['payment']['entity']['id'],
            orderId: payload['payload']['payment']['entity']['order_id'],
            signature: payload['payload']['payment']['entity']['signature'] ?? '',
            paymentData: payload['payload']['payment']['entity'],
          );
          
        case 'payment.failed':
          return await handlePaymentFailure(
            orderId: payload['payload']['payment']['entity']['order_id'],
            errorData: payload['payload']['payment']['entity'],
          );
          
        case 'order.paid':
          return await handleOrderCreated(
            orderId: payload['payload']['order']['entity']['id'],
            amount: (payload['payload']['order']['entity']['amount'] / 100).toDouble(),
            orderData: payload['payload']['order']['entity'],
          );
          
        default:
          if (kDebugMode) {
            log('⚠️ [RAZORPAY_WEBHOOK] Unhandled event type: $eventType');
          }
          return {'status': 'ignored', 'event': eventType};
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_WEBHOOK] Webhook processing failed: $e');
      }
      rethrow;
    }
  }

  /// Test webhook connectivity following SSO-Flutter pattern
  static Future<bool> testWebhookConnectivity() async {
    try {
      final headers = await _getAuthHeaders();

      final response = await http.get(
        Uri.parse('${Environment.config.chsoneOperatorsUrl}webhook/razorpay/health'),
        headers: headers,
      );

      final isHealthy = response.statusCode == 200;

      if (kDebugMode) {
        log(isHealthy
            ? '✅ [$_logName] Webhook connectivity test passed'
            : '❌ [$_logName] Webhook connectivity test failed: ${response.statusCode}');
      }

      return isHealthy;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [$_logName] Connectivity test error: $e');
      }
      return false;
    }
  }

  /// Get authentication headers following SSO-Flutter pattern
  static Future<Map<String, String>> _getAuthHeaders() async {
    try {
      final accessToken = await AuthTokenManager.getBestAvailableToken();
      final userProfile = await SsoStorage.getUserProfile();
      final apiToken = userProfile?['api_token'];

      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-MClient': '14', // SSO-Flutter client identifier
      };

      if (accessToken != null) {
        headers['x-access-token'] = accessToken;
      }

      if (apiToken != null) {
        headers['x-api-token'] = apiToken;
      }

      return headers;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [$_logName] Failed to get auth headers: $e');
      }

      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    }
  }
}
