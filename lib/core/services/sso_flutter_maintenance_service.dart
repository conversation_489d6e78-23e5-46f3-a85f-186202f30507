import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../constants.dart';
import './chsone_auth_service.dart';
import './keycloak_service.dart';
import './onepay_config_service.dart';
import './api_service.dart';
import './razorpay_service.dart';
import '../../utils/storage/auth_integration_service.dart';
import '../../utils/storage/sso_storage.dart';
import 'chsone_operator_headers.dart';

/// SSO-Flutter compatible maintenance service
/// Implements the exact API calls and flow from sso-flutter maintenance system
class SSOFlutterMaintenanceService {
  static const String _chsoneOperatorsBaseUrl = 'https://chsone.in/api/v1/';
  static const String _chsoneResidentBaseUrl =
      'https://api.chsone.in/residentapi/v2/';

  /// Get maintenance invoices (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/chsone_api.dart getMaintainceDues()
  static Future<List<Map<String, dynamic>>> getMaintenanceInvoices(
      {int retryCount = 0}) async {
    const maxRetries = 3;

    try {
      log('📡 [SSO-Flutter API] Fetching maintenance invoices... (attempt ${retryCount + 1}/$maxRetries)');

      // Get user profile to extract unit_id and token (required for maintenance bills)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for maintenance bills access');
      }

      // Get the unit_id from user profile (required by sso-flutter maintenance/invoices endpoint)
      String? unitId;
      log('🔍 [SSO-Flutter API] User profile keys: ${userProfile.keys.toList()}');
      log('🔍 [SSO-Flutter API] User profile data: $userProfile');

      // Try multiple ways to get unit_id
      if (userProfile['unit_id'] != null) {
        unitId = userProfile['unit_id'].toString();
        log('🔍 [SSO-Flutter API] Found unit_id in profile: $unitId');
      } else if (userProfile['selected_unit'] != null &&
          userProfile['selected_unit']['id'] != null) {
        unitId = userProfile['selected_unit']['id'].toString();
        log('🔍 [SSO-Flutter API] Found unit_id in selected_unit: $unitId');
      } else if (userProfile['chsone_session'] != null &&
          userProfile['chsone_session']['unit_id'] != null) {
        unitId = userProfile['chsone_session']['unit_id'].toString();
        log('🔍 [SSO-Flutter API] Found unit_id in chsone_session: $unitId');
      } else {
        // Try to use a default unit_id or get from society account
        log('⚠️ [SSO-Flutter API] No unit_id found in profile, trying to get from society account...');
        try {
          final email =
              userProfile['email'] ?? userProfile['preferred_username'] ?? '';
          if (email.isNotEmpty) {
            final accounts = await getAccountSuggestions(email: email);
            if (accounts.isNotEmpty && accounts.first['unit_id'] != null) {
              unitId = accounts.first['unit_id'].toString();
              log('🔍 [SSO-Flutter API] Found unit_id from society account: $unitId');
            }
          }
        } catch (e) {
          log('⚠️ [SSO-Flutter API] Failed to get unit_id from society account: $e');
        }
      }

      // If still no unit_id, try using a default or user_id as fallback
      if (unitId == null || unitId.isEmpty) {
        log('⚠️ [SSO-Flutter API] No unit_id found, trying fallback approaches...');

        // Try using user_id as unit_id (some systems use this pattern)
        final userId = await getNumericUserId(userProfile);
        if (userId != null && userId.isNotEmpty) {
          unitId = userId;
          log('🔍 [SSO-Flutter API] Using user_id as unit_id fallback: $unitId');
        } else {
          throw Exception(
              'Unit ID required for maintenance bills access. Please ensure you have selected a unit or contact support.');
        }
      }

      log('🔍 [SSO-Flutter API] Using unit_id for maintenance bills: $unitId');

      // Use exact sso-flutter API endpoint: chsoneResidentUrl + maintenance/invoices
      final url = '${_chsoneResidentBaseUrl}maintenance/invoices';
      log('🔗 [SSO-Flutter API] URL: $url');

      // Get access token for sso-flutter maintenance/invoices endpoint
      String? accessToken;
      try {
        // Try to get CHSONE access token first
        final chsoneService = ChsoneAuthService.instance;
        accessToken = await chsoneService.getChsoneAccessToken();

        if (accessToken == null || accessToken.isEmpty) {
          // Fallback to other token sources
          accessToken = await AuthIntegrationService.getBestAccessToken();
        }

        if (accessToken == null || accessToken.isEmpty) {
          throw Exception('No access token available');
        }

        log('✅ [SSO-Flutter API] Access token obtained successfully');
      } catch (e) {
        log('❌ [SSO-Flutter API] Failed to get access token: $e');

        // If this is not the first attempt and we still can't get token, give up
        if (retryCount > 0) {
          throw Exception(
              'Authentication error: Unable to get access token. Please check your login status.');
        }

        // Try to initialize configuration and retry
        log('🔄 [SSO-Flutter API] Attempting to initialize configuration...');
        try {
          final configService = OnePayConfigService.instance;
          await configService.initialize();
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        } catch (configError) {
          throw Exception('Configuration initialization failed: $configError');
        }
      }

      // Build query parameters as required by sso-flutter maintenance/invoices endpoint
      final queryParams = {
        'token': accessToken,
        'unit_id': unitId,
      };

      final uri = Uri.parse(url).replace(queryParameters: queryParams);
      log('🔗 [SSO-Flutter API] Final URL with params: $uri');

      // Use simple headers for resident API
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      final response = await http
          .get(
            uri,
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      log('📊 [SSO-Flutter API] Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Maintenance invoices fetched successfully');

        if (responseData['data'] != null) {
          final invoices =
              List<Map<String, dynamic>>.from(responseData['data']);
          log('📋 [SSO-Flutter API] Found ${invoices.length} maintenance invoices');
          return invoices;
        }

        log('⚠️ [SSO-Flutter API] No invoice data in response');
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null && retryCount < maxRetries) {
          // Retry with new token
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        }
        throw Exception('Authentication failed - please log in again');
      } else if (response.statusCode == 500) {
        log('❌ [SSO-Flutter API] Server error (500): ${response.body}');

        // Parse server error for more details
        String serverErrorDetails = 'Internal server error';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            serverErrorDetails = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            serverErrorDetails = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        // Retry on server errors (up to maxRetries)
        if (retryCount < maxRetries) {
          log('🔄 [SSO-Flutter API] Retrying after server error... (${retryCount + 1}/$maxRetries)');
          await Future.delayed(
              Duration(seconds: (retryCount + 1) * 2)); // Exponential backoff
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        }

        throw Exception(
            'Server temporarily unavailable: $serverErrorDetails. Please try again later.');
      } else {
        log('❌ [SSO-Flutter API] Error response: ${response.body}');

        // Parse error response for better user messages
        String errorMessage = 'Failed to fetch maintenance invoices';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            errorMessage = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        if (response.statusCode == 404) {
          throw Exception('No maintenance bills found for your account');
        } else if (response.statusCode == 403) {
          throw Exception(
              'Access denied: You may not have permission to view maintenance bills. Please ensure you have a valid numeric user_id and proper authentication.');
        } else {
          throw Exception('$errorMessage (Error ${response.statusCode})');
        }
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error: $e');

      // If it's already our custom exception, re-throw it
      if (e is Exception && e.toString().contains('Exception:')) {
        rethrow;
      }

      // For network errors, provide user-friendly message
      if (e.toString().contains('TimeoutException') ||
          e.toString().contains('SocketException')) {
        throw Exception(
            'Network connection error: Please check your internet connection and try again');
      }

      // Generic error fallback
      throw Exception('Unable to fetch maintenance bills: ${e.toString()}');
    }
  }

  /// Calculate total society payment amount (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart calPayableMaintenanceAmt()
  static Future<Map<String, dynamic>> calculateTotalSocietyPaymentAmount({
    required double amount,
  }) async {
    try {
      log('💰 [SSO-Flutter API] Calculating total society payment amount...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for calculation');
      }

      final userId = userProfile['user_id'].toString();

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint and parameters
      final url =
          '${_chsoneOperatorsBaseUrl}calculateTotalSocietyPaymentAmount?amount=$amount&user_id=$userId';
      log('🔗 [SSO-Flutter API] Calculation URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      log('📊 [SSO-Flutter API] Calculation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Amount calculated successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to calculate amount: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error calculating amount: $e');
      rethrow;
    }
  }

  /// Initiate society payment (sso-flutter compatible) - OPTIMIZED
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart initiateSocietyPayment()
  static Future<Map<String, dynamic>> initiateSocietyPayment({
    required String accountName,
    required double totalPayableAmount,
    required double actualAmount,
    required String accountId,
    String? pan,
    String? note,
  }) async {
    try {
      // Reduce logging for performance - only essential logs
      if (kDebugMode) {
        log('🚀 [PAYMENT] Initiating payment for $accountName');
      }

      // Optimized user_id generation - cache email for performance
      final email = accountName.contains('@')
          ? accountName
          : '<EMAIL>';

      // Use a more efficient hash calculation
      final userId = _generateOptimizedUserId(email);

      // Build request in background to avoid UI blocking
      final requestData = await _buildPaymentRequest(
        accountName: accountName,
        totalPayableAmount: totalPayableAmount,
        actualAmount: actualAmount,
        accountId: accountId,
        userId: userId,
        pan: pan,
        note: note,
      );

      final response = await http.Response.fromStream(requestData);

      if (kDebugMode) {
        log('📊 [PAYMENT] Response: ${response.statusCode}');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        if (kDebugMode) {
          log('✅ [PAYMENT] Payment initiated successfully');
        }
        return responseData['data'] ?? responseData;
      } else {
        if (kDebugMode) {
          log('❌ [PAYMENT] Failed: ${response.statusCode}');
        }
        throw Exception(
            'Payment initiation failed: HTTP ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [PAYMENT] Error: $e');
      }
      rethrow;
    }
  }

  /// Optimized user ID generation to reduce computation time
  static String _generateOptimizedUserId(String email) {
    // Use a simpler, faster hash calculation
    final hash = email.hashCode.abs();
    return (hash % 899999 + 100000).toString(); // 6-digit number
  }

  /// Build payment request in background to avoid blocking UI
  static Future<http.StreamedResponse> _buildPaymentRequest({
    required String accountName,
    required double totalPayableAmount,
    required double actualAmount,
    required String accountId,
    required String userId,
    String? pan,
    String? note,
  }) async {
    final request = http.MultipartRequest(
      'POST',
      Uri.parse('${_chsoneOperatorsBaseUrl}initiateSocietyPayment'),
    );

    // Get headers efficiently
    final headers = await ChsOneOperatorHeaders.build();
    request.headers.addAll(headers);

    // Add form fields efficiently
    request.fields.addAll({
      'totalPayableAmount': totalPayableAmount.toString(),
      'account_name': accountName,
      'pan': pan ?? '',
      'account_id': accountId,
      'note': note ?? '',
      'actualAmount': actualAmount.toString(),
      'user_id': userId,
    });

    return await request.send();
  }

  /// Complete society payment (sso-flutter compatible) - ENHANCED
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart completeSocietyPayment()
  static Future<Map<String, dynamic>> completeSocietyPayment({
    required String orderId,
    required String paymentId,
    required double totalPayableAmount,
    required String accountId,
  }) async {
    try {
      if (kDebugMode) {
        log('✅ [PAYMENT_COMPLETION] Starting payment completion...');
        log('   - Order ID: $orderId');
        log('   - Payment ID: $paymentId');
        log('   - Amount: ₹$totalPayableAmount');
        log('   - Account ID: $accountId');
      }

      // Validate required parameters
      if (orderId.isEmpty || paymentId.isEmpty || accountId.isEmpty) {
        throw Exception('Missing required parameters for payment completion');
      }

      // Get user profile with optimized caching
      final userProfile = await _getCachedUserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for payment completion');
      }

      // Get numeric user ID
      final userId = await _getOptimizedUserId(userProfile);
      if (userId == null) {
        throw Exception('Valid user ID required for payment completion');
      }

      // Build payload exactly like sso-flutter
      final completionData = {
        'user_id': userId,
        'order_id': orderId,
        'payment_id': paymentId,
        'totalPayableAmount': totalPayableAmount.toString(),
        'account_id': accountId,
        'status': 'completed',
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (kDebugMode) {
        log('📋 [PAYMENT_COMPLETION] Completion payload: $completionData');
      }

      final headers = await ChsOneOperatorHeaders.build();
      headers['Content-Type'] = 'application/json';

      // Use exact sso-flutter API endpoint
      final url = '${_chsoneOperatorsBaseUrl}completeSocietyPayment';

      if (kDebugMode) {
        log('🔗 [PAYMENT_COMPLETION] Complete URL: $url');
      }

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(completionData),
      ).timeout(const Duration(seconds: 30));

      if (kDebugMode) {
        log('📊 [PAYMENT_COMPLETION] Response status: ${response.statusCode}');
        log('📊 [PAYMENT_COMPLETION] Response body: ${response.body}');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);

        // Enhanced response validation
        if (responseData['status'] == 'success' || responseData['data'] != null) {
          if (kDebugMode) {
            log('🎉 [PAYMENT_COMPLETION] Payment completed successfully');
          }

          // Return enhanced response with additional metadata
          final result = responseData['data'] ?? responseData;
          result['completion_timestamp'] = DateTime.now().toIso8601String();
          result['original_order_id'] = orderId;
          result['original_payment_id'] = paymentId;

          return result;
        } else {
          throw Exception('Payment completion failed: ${responseData['message'] ?? 'Unknown error'}');
        }
      } else if (response.statusCode == 400) {
        final errorData = json.decode(response.body);
        throw Exception('Invalid payment data: ${errorData['message'] ?? 'Bad request'}');
      } else if (response.statusCode == 401) {
        throw Exception('Authentication failed. Please log in again.');
      } else if (response.statusCode == 404) {
        throw Exception('Payment not found. Please verify the payment details.');
      } else if (response.statusCode >= 500) {
        throw Exception('Server error. Please try again later.');
      } else {
        throw Exception('Payment completion failed: HTTP ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [PAYMENT_COMPLETION] Error: $e');
      }

      // Provide more specific error messages
      if (e.toString().contains('TimeoutException')) {
        throw Exception('Payment completion timed out. Please check your connection and try again.');
      } else if (e.toString().contains('SocketException')) {
        throw Exception('Network error during payment completion. Please check your internet connection.');
      }

      rethrow;
    }
  }

  /// Get society account details (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart getSocietyAcDetails()
  static Future<List<Map<String, dynamic>>> getSocietyAccountDetails({
    required String email,
  }) async {
    try {
      log('🏢 [SSO-Flutter API] Fetching society account details...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for account details');
      }

      final userId = userProfile['user_id'].toString();

      // Build header map (includes x-access-token & x-api-token)
      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint and parameters
      final url = '${_chsoneOperatorsBaseUrl}getSocietyAccount';
      log('🔗 [SSO-Flutter API] Account URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...headers,
          'X-User-ID': userId,
        },
        body: json.encode({
          'user_id': userId,
          'email': email,
          'source': 'CUBEONE',
        }),
      );

      log('📊 [SSO-Flutter API] Account response status: ${response.statusCode}');
      log('📊 [SSO-Flutter API] Account response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Account details fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null) {
          // Retry with new token
          return getSocietyAccountDetails(email: email);
        }
        throw Exception('Authentication failed - please log in again');
      } else {
        final errorBody = json.decode(response.body);
        final errorMessage =
            errorBody['message'] ?? errorBody['error'] ?? 'Unknown error';
        throw Exception('Failed to fetch account details: $errorMessage');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching account details: $e');
      rethrow;
    }
  }

  /// Create society account (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart createSocietyAc()
  static Future<Map<String, dynamic>> createSocietyAccount({
    required String societyName,
    required String email,
    required String accountNumber,
    required String ifscCode,
  }) async {
    try {
      log('🏗️ [SSO-Flutter API] Creating society account...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for account creation');
      }

      // Build payload exactly like sso-flutter
      final accountData = {
        'user_id': userProfile['user_id'].toString(),
        'society_name': societyName,
        'email': email,
        'account_number': accountNumber,
        'ifsc_code': ifscCode,
      };

      log('📋 [SSO-Flutter API] Account creation payload: $accountData');

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint
      final url = '${_chsoneOperatorsBaseUrl}addSocietyAccount';
      log('🔗 [SSO-Flutter API] Create account URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(accountData),
      );

      log('📊 [SSO-Flutter API] Create account response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        log('🎉 [SSO-Flutter API] Society account created successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to create society account: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error creating society account: $e');
      rethrow;
    }
  }

  /// Get account suggestions based on email (sso-flutter compatible) - OPTIMIZED
  /// Matches: sso-flutter/lib/ui/module/sso/utility_bill_payment/maintenance_payment/owner_details.dart getAccountDetails()
  static Future<List<Map<String, dynamic>>> getAccountSuggestions({
    required String email,
  }) async {
    try {
      if (kDebugMode) {
        log('🔍 [ACCOUNTS] Getting suggestions for: $email');
      }

      // Optimized profile retrieval with caching
      final userProfile = await _getCachedUserProfile();
      if (userProfile == null) {
        if (kDebugMode) {
          log('⚠️ [ACCOUNTS] No user profile available');
        }
        return [];
      }

      // Optimized user ID retrieval
      final userId = await _getOptimizedUserId(userProfile);

      // Build URL efficiently
      final url = _buildAccountSuggestionsUrl(userId, email);

      if (kDebugMode) {
        log('🔗 [ACCOUNTS] URL: $url');
      }

      final headers = await ChsOneOperatorHeaders.build();
      final response = await http.get(Uri.parse(url), headers: headers);

      if (kDebugMode) {
        log('📊 [ACCOUNTS] Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        if (responseData['data'] != null) {
          // Optimized data parsing
          final accounts = _parseAccountsData(responseData['data']);

          if (kDebugMode) {
            log('✅ [ACCOUNTS] Found ${accounts.length} accounts');
          }

          return accounts;
        }

        return [];
      } else if (response.statusCode == 404) {
        // No accounts found - normal case
        if (kDebugMode) {
          log('ℹ️ [ACCOUNTS] No accounts found');
        }
        return [];
      } else {
        if (kDebugMode) {
          log('❌ [ACCOUNTS] Error: ${response.statusCode}');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ [ACCOUNTS] Exception: $e');
      }
      return [];
    }
  }

  /// Optimized URL building for account suggestions
  static String _buildAccountSuggestionsUrl(String? userId, String email) {
    final baseUrl = '${_chsoneOperatorsBaseUrl}getSocietyAccount';
    if (userId != null && userId.isNotEmpty && userId != 'null') {
      return '$baseUrl?user_id=$userId&email=$email';
    } else {
      return '$baseUrl?email=$email';
    }
  }

  /// Optimized account data parsing
  static List<Map<String, dynamic>> _parseAccountsData(dynamic data) {
    if (data is List) {
      return List<Map<String, dynamic>>.from(data);
    } else if (data is Map) {
      return [Map<String, dynamic>.from(data)];
    }
    return [];
  }

  /// Cached user profile retrieval to avoid repeated calls
  static Map<String, dynamic>? _cachedUserProfile;
  static DateTime? _cacheTimestamp;
  static const Duration _cacheTimeout = Duration(minutes: 5);

  static Future<Map<String, dynamic>?> _getCachedUserProfile() async {
    final now = DateTime.now();

    // Check if cache is valid
    if (_cachedUserProfile != null &&
        _cacheTimestamp != null &&
        now.difference(_cacheTimestamp!) < _cacheTimeout) {
      return _cachedUserProfile;
    }

    // Refresh cache
    _cachedUserProfile = await getSSO_UserProfile();
    _cacheTimestamp = now;

    return _cachedUserProfile;
  }

  /// Optimized user ID retrieval
  static Future<String?> _getOptimizedUserId(Map<String, dynamic> userProfile) async {
    // Quick check for existing numeric user_id
    if (userProfile['user_id'] != null) {
      final userIdValue = userProfile['user_id'];
      if (userIdValue is int) {
        return userIdValue.toString();
      } else if (userIdValue is String && int.tryParse(userIdValue) != null) {
        return userIdValue;
      }
    }

    // Fallback to full numeric user ID retrieval
    return await getNumericUserId(userProfile);
  }

  /// Get maintenance payment history (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart getSocietyPayments()
  static Future<List<Map<String, dynamic>>> getMaintenancePaymentHistory({
    String searchText = '',
  }) async {
    try {
      log('📜 [SSO-Flutter API] Fetching maintenance payment history...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for payment history');
      }

      final userId = userProfile['user_id'].toString();

      // Use exact sso-flutter API endpoint and parameters
      final url =
          '${_chsoneOperatorsBaseUrl}getSocietyPayments?user_id=$userId&search=$searchText';
      log('🔗 [SSO-Flutter API] History URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: await ChsOneOperatorHeaders.build(),
      );

      log('📊 [SSO-Flutter API] History response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Payment history fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else {
        throw Exception(
            'Failed to fetch payment history: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching payment history: $e');
      rethrow;
    }
  }

  // Private helper methods

  /// Get numeric user_id using sso-flutter mapping logic - OPTIMIZED
  /// Maps Keycloak UUID to numeric user_id (like 73553)
  static Future<String?> getNumericUserId(
      Map<String, dynamic> userProfile) async {
    try {
      if (kDebugMode) {
        log('🔍 [USER_ID] Mapping user_id from profile...');
      }

      // Method 1: Quick check for existing numeric user_id
      if (userProfile['user_id'] != null) {
        final userIdValue = userProfile['user_id'];
        if (userIdValue is int) {
          return userIdValue.toString();
        } else if (userIdValue is String && int.tryParse(userIdValue) != null) {
          return userIdValue;
        }
      }

      // Method 2: Check chsone_session first (faster than external calls)
      if (userProfile['chsone_session'] != null) {
        final chsoneSession = userProfile['chsone_session'];
        if (chsoneSession['user_id'] != null) {
          final sessionUserId = chsoneSession['user_id'].toString();
          if (int.tryParse(sessionUserId) != null) {
            if (kDebugMode) {
              log('🔍 [USER_ID] Found from chsone_session: $sessionUserId');
            }
            return sessionUserId;
          }
        }
      }

      // Method 3: Try ChsoneAuthService (cached data)
      try {
        final chsoneService = ChsoneAuthService.instance;
        final authData = await chsoneService.getStoredAuthData();
        if (authData != null && authData['user_id'] != null) {
          final chsoneUserId = authData['user_id'].toString();
          if (int.tryParse(chsoneUserId) != null) {
            if (kDebugMode) {
              log('🔍 [USER_ID] Found from ChsoneAuthService: $chsoneUserId');
            }
            return chsoneUserId;
          }
        }
      } catch (e) {
        // Continue to next method if this fails
        if (kDebugMode) {
          log('⚠️ [USER_ID] ChsoneAuthService failed: $e');
        }
      }

      // Method 4: Check Keycloak data (external call - slower)
      try {
        final keycloakData = await KeycloakService.getUserData();
        if (keycloakData != null && keycloakData['old_sso_user_id'] != null) {
          final oldSsoUserId = keycloakData['old_sso_user_id'].toString();
          if (int.tryParse(oldSsoUserId) != null) {
            if (kDebugMode) {
              log('🔍 [USER_ID] Found old_sso_user_id: $oldSsoUserId');
            }
            return oldSsoUserId;
          }
        }
      } catch (e) {
        // Continue to next method if this fails
        if (kDebugMode) {
          log('⚠️ [USER_ID] Keycloak lookup failed: $e');
        }
      }

      // Method 5: Last resort - SSO API call (slowest)
      try {
        final numericUserId = await _fetchNumericUserIdFromSSO();
        if (numericUserId != null) {
          if (kDebugMode) {
            log('🔍 [USER_ID] Found from SSO API: $numericUserId');
          }
          return numericUserId;
        }
      } catch (e) {
        if (kDebugMode) {
          log('⚠️ [USER_ID] SSO API failed: $e');
        }
      }

      if (kDebugMode) {
        log('❌ [USER_ID] No numeric user_id found');
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [USER_ID] Error: $e');
      }
      return null;
    }
  }

  /// Fetch numeric user_id from SSO API using Keycloak token
  static Future<String?> _fetchNumericUserIdFromSSO() async {
    try {
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken == null) return null;

      // Try to call SSO profile endpoint to get numeric user_id
      final headers = {
        'Authorization': 'Bearer $keycloakToken',
        'Content-Type': 'application/json',
      };

      // Try multiple SSO profile endpoints
      final endpoints = [
        'https://chsone.in/api/v1/auth/profile',
        'https://chsone.in/api/v1/users/profile',
        'https://stgsso.cubeone.in/auth/realms/fstech/protocol/openid-connect/userinfo',
      ];

      for (final endpoint in endpoints) {
        try {
          log('🔗 [SSO-Flutter API] Trying profile endpoint: $endpoint');
          final response =
              await http.get(Uri.parse(endpoint), headers: headers);

          if (response.statusCode == 200) {
            final profileData = json.decode(response.body);
            log('📊 [SSO-Flutter API] Profile response: $profileData');

            // Look for numeric user_id in response
            if (profileData['user_id'] != null) {
              final userId = profileData['user_id'].toString();
              if (int.tryParse(userId) != null) {
                return userId;
              }
            }

            // Look for id field
            if (profileData['id'] != null) {
              final userId = profileData['id'].toString();
              if (int.tryParse(userId) != null) {
                return userId;
              }
            }
          }
        } catch (e) {
          log('⚠️ [SSO-Flutter API] Endpoint $endpoint failed: $e');
          continue;
        }
      }

      return null;
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching numeric user_id from SSO: $e');
      return null;
    }
  }

  /// Get SSO-Flutter compatible authentication token
  static Future<String?> _getSSOAuthToken() async {
    try {
      // First try getting token from AuthIntegrationService
      final token = await AuthIntegrationService.getBestAccessToken();
      if (token != null && token.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from AuthIntegrationService');
        return token;
      }

      // Try CHSONE token
      final chsoneToken =
          await ChsoneAuthService.instance.getChsoneAccessToken();
      if (chsoneToken != null && chsoneToken.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from CHSONE');
        // Save to SSO storage for future use
        await SsoStorage.saveTokenObject({
          'access_token': chsoneToken,
          'token_type': 'Bearer',
          'synced_from': 'chsone',
          'synced_at': DateTime.now().toIso8601String(),
        });
        return chsoneToken;
      }

      // Try Keycloak token
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null && keycloakToken.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from Keycloak');
        // Save to SSO storage
        await SsoStorage.saveTokenObject({
          'access_token': keycloakToken,
          'token_type': 'Bearer',
          'synced_from': 'keycloak',
          'synced_at': DateTime.now().toIso8601String(),
        });
        return keycloakToken;
      }

      log('⚠️ [SSO-Flutter Auth] No valid authentication token available');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter Auth] Error getting auth token: $e');
      return null;
    }
  }

  /// Refresh token
  static Future<String?> _refreshToken() async {
    try {
      log('🔄 [SSO-Flutter Auth] Attempting token refresh...');

      // Try CHSONE refresh first
      final chsoneService = ChsoneAuthService.instance;
      final chsoneToken = await chsoneService.getChsoneAccessToken();
      if (chsoneToken != null) {
        log('✅ [SSO-Flutter Auth] Refreshed CHSONE token');
        await AuthIntegrationService.syncChsoneToSsoStorage();
        return chsoneToken;
      }

      // Try Keycloak refresh
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null) {
        log('✅ [SSO-Flutter Auth] Got new Keycloak token');
        await AuthIntegrationService.syncKeycloakToSsoStorage();
        return keycloakToken;
      }

      log('⚠️ [SSO-Flutter Auth] Token refresh failed');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter Auth] Error refreshing token: $e');
      return null;
    }
  }

  /// Get SSO-Flutter compatible user profile
  /// Simulates SsoStorage.getUserProfile() from sso-flutter
  static Future<Map<String, dynamic>?> getSSO_UserProfile() async {
    try {
      log('👤 [SSO-Flutter Profile] Getting user profile...');

      // First try getting from SSO storage
      try {
        final ssoProfile = await SsoStorage.getUserProfile();
        if (ssoProfile != null &&
            ssoProfile.isNotEmpty &&
            ssoProfile['user_id'] != null) {
          log('✅ [SSO-Flutter] Got user profile from SSO storage with user_id: ${ssoProfile['user_id']}');
          return ssoProfile;
        }
      } catch (e) {
        log('⚠️ [SSO-Flutter] SSO storage not available: $e');
      }

      // Get Keycloak user info and create SSO-Flutter compatible profile
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null) {
        final userInfo = await KeycloakService.getUserInfo();
        if (userInfo != null) {
          log('✅ [SSO-Flutter] Got user info from Keycloak: $userInfo');

          // Create SSO-Flutter compatible profile with numeric user_id
          final ssoProfile = await _createSSOProfile(userInfo);
          if (ssoProfile != null) {
            await SsoStorage.saveUserProfile(ssoProfile);
            return ssoProfile;
          }
        }
      }

      log('⚠️ [SSO-Flutter] No user profile available');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter] Error getting user profile: $e');
      return null;
    }
  }

  /// Create SSO-Flutter compatible profile from Keycloak user info
  static Future<Map<String, dynamic>?> _createSSOProfile(
      Map<String, dynamic> keycloakUserInfo) async {
    try {
      log('🔄 [SSO-Flutter] Creating SSO profile from Keycloak data...');

      // Try to get numeric user_id from various sources
      String? numericUserId;

      // Method 1: Try to get from API service (old_sso_user_id)
      try {
        final apiService = ApiService.instance;
        numericUserId = await apiService.getUserId();
        if (numericUserId != null &&
            numericUserId.isNotEmpty &&
            numericUserId != 'null') {
          log('🔍 [SSO-Flutter] Found numeric user_id from ApiService: $numericUserId');
        } else {
          numericUserId = null;
        }
      } catch (e) {
        log('⚠️ [SSO-Flutter] ApiService getUserId failed: $e');
      }

      // Method 2: Try to fetch from SSO API endpoints
      if (numericUserId == null) {
        numericUserId = await _fetchNumericUserIdFromSSO();
      }

      // Method 3: Generate a numeric user_id from email hash (fallback)
      if (numericUserId == null) {
        final email = keycloakUserInfo['email']?.toString();
        if (email != null) {
          // Create a consistent numeric ID from email hash
          final emailHash = email.hashCode.abs();
          numericUserId =
              (emailHash % 999999 + 100000).toString(); // 6-digit number
          log('🔄 [SSO-Flutter] Generated numeric user_id from email hash: $numericUserId');
        }
      }

      if (numericUserId == null) {
        log('❌ [SSO-Flutter] Could not generate numeric user_id');
        return null;
      }

      // Create SSO-Flutter compatible profile
      final ssoProfile = {
        'user_id': int.tryParse(numericUserId) ?? int.parse(numericUserId),
        'email': keycloakUserInfo['email'],
        'first_name': keycloakUserInfo['given_name'] ??
            keycloakUserInfo['name']?.toString().split(' ').first ??
            'User',
        'last_name': keycloakUserInfo['family_name'] ??
            keycloakUserInfo['name']?.toString().split(' ').skip(1).join(' ') ??
            '',
        'username':
            keycloakUserInfo['preferred_username'] ?? keycloakUserInfo['email'],
        'mobile': '9191919191', // Default mobile
        'city': 'Mumbai', // Default city
        'avatar_large': '', // Default avatar
        'sub': keycloakUserInfo['sub'],
        'email_verified': keycloakUserInfo['email_verified'] ?? false,
        // Add original Keycloak data for reference
        '_keycloak_data': keycloakUserInfo,
      };

      log('✅ [SSO-Flutter] Created SSO profile with user_id: ${ssoProfile['user_id']}');
      log('🔍 [SSO-Flutter] SSO profile: $ssoProfile');

      return ssoProfile;
    } catch (e) {
      log('❌ [SSO-Flutter] Error creating SSO profile: $e');
      return null;
    }
  }

  /// Process Razorpay payment for maintenance (unified method)
  /// Combines initiation, Razorpay checkout, and completion in one flow
  static Future<Map<String, dynamic>> processRazorpayMaintenancePayment({
    required String accountName,
    required double totalPayableAmount,
    required double actualAmount,
    required String accountId,
    String? pan,
    String? note,
    required Function(PaymentSuccessResponse) onSuccess,
    required Function(PaymentFailureResponse) onError,
  }) async {
    try {
      if (kDebugMode) {
        log('🚀 [RAZORPAY_MAINTENANCE] Starting Razorpay maintenance payment flow');
      }

      // Step 1: Initiate payment through SSO-Flutter service
      final paymentData = await initiateSocietyPayment(
        accountName: accountName,
        totalPayableAmount: totalPayableAmount,
        actualAmount: actualAmount,
        accountId: accountId,
        pan: pan,
        note: note,
      );

      if (paymentData['order_id'] == null) {
        throw Exception('Failed to get order ID from payment initiation');
      }

      // Step 2: Setup Razorpay service with callbacks
      final razorpayService = RazorpayService.instance;
      await razorpayService.initialize();

      razorpayService.setCallbacks(
        onSuccess: (response) async {
          try {
            // Step 3: Verify payment signature (SSO-Flutter security pattern)
            final isValidSignature = await RazorpayService.instance.verifyPaymentSignature(
              paymentId: response.paymentId!,
              orderId: response.orderId!,
              signature: response.signature ?? '',
            );

            if (!isValidSignature) {
              throw Exception('Payment signature verification failed');
            }

            // Step 4: Complete payment on success
            final completionResult = await completeSocietyPayment(
              orderId: response.orderId!,
              paymentId: response.paymentId!,
              totalPayableAmount: totalPayableAmount,
              accountId: accountId,
            );

            if (kDebugMode) {
              log('✅ [RAZORPAY_MAINTENANCE] Payment completed successfully');
            }

            onSuccess(response);
          } catch (e) {
            if (kDebugMode) {
              log('❌ [RAZORPAY_MAINTENANCE] Payment completion failed: $e');
            }

            // Create error response for completion failure
            onError(PaymentFailureResponse(
              999, // Custom error code for completion failure
              'Payment successful but completion failed: $e',
              {'error': 'COMPLETION_FAILED'},
            ));
          }
        },
        onError: onError,
      );

      // Step 3: Open Razorpay checkout
      await razorpayService.openMaintenancePayment(
        amount: totalPayableAmount,
        accountName: accountName,
        accountId: accountId,
        pan: pan,
        note: note,
      );

      return paymentData;
    } catch (e) {
      if (kDebugMode) {
        log('❌ [RAZORPAY_MAINTENANCE] Payment flow failed: $e');
      }
      rethrow;
    }
  }

  /// Get society payments history (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/chsone_api.dart getSocietyPayments()
  static Future<List<Map<String, dynamic>>> getSocietyPayments({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      log('📡 [SSO-Flutter API] Fetching society payments (page: $page, limit: $limit)...');

      // Use exact sso-flutter API endpoint
      final url =
          '${_chsoneResidentBaseUrl}society/payments?page=$page&limit=$limit';
      log('🔗 [SSO-Flutter API] URL: $url');

      // Get headers with x-access-token and x-api-token (sso-flutter style)
      final headers = await ChsOneOperatorHeaders.build();

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      log('📊 [SSO-Flutter API] Response status: ${response.statusCode}');
      log('📊 [SSO-Flutter API] Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Society payments fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null) {
          // Retry with new token
          return getSocietyPayments(page: page, limit: limit);
        }
        throw Exception('Authentication failed - please log in again');
      } else {
        log('❌ [SSO-Flutter API] Error response: ${response.body}');

        // Parse error response for better user messages
        String errorMessage = 'Failed to fetch payment history';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            errorMessage = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        if (response.statusCode == 500) {
          throw Exception('Server error: Please try again later');
        } else if (response.statusCode == 404) {
          throw Exception('No payment history found');
        } else {
          throw Exception('$errorMessage (${response.statusCode})');
        }
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error: $e');
      throw e;
    }
  }
}
