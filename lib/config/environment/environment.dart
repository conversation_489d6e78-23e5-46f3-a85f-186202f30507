import 'base_config.dart';
import 'development.dart';
import 'production.dart';

enum EnvironmentType { development, staging, production }

class Environment {
  static EnvironmentType _environmentType = EnvironmentType.production;
  static BaseConfig? _config;

  static void setEnvironment(EnvironmentType type) {
    _environmentType = type;
    _config = null; // Reset config when environment changes
  }

  static BaseConfig get config {
    _config ??= _createConfig();
    return _config!;
  }

  static BaseConfig _createConfig() {
    switch (_environmentType) {
      case EnvironmentType.development:
        return DevelopmentConfig(); // Now using proper development config
      case EnvironmentType.staging:
        return ProductionConfig(); // Use production for now, add StagingConfig later
      case EnvironmentType.production:
        return ProductionConfig();
    }
  }

  static bool get isProduction => _environmentType == EnvironmentType.production;
  static bool get isDevelopment => _environmentType == EnvironmentType.development;
  static bool get isStaging => _environmentType == EnvironmentType.staging;
} 