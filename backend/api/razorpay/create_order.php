<?php
/**
 * Razorpay Order Creation Endpoint
 * Following SSO-Flutter patterns for CHSOne backend integration
 * 
 * Endpoint: POST /api/v1/createRazorpayOrder
 * Headers: x-access-token, x-api-token, X-MClient
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, x-access-token, x-api-token, X-MClient');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

require_once '../config/database.php';
require_once '../config/razorpay_config.php';
require_once '../utils/auth_validator.php';
require_once '../utils/logger.php';

try {
    // Validate authentication headers (SSO-Flutter pattern)
    $auth_result = AuthValidator::validateRequest();
    if (!$auth_result['valid']) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication failed', 'details' => $auth_result['message']]);
        exit();
    }

    // Get request body
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required_fields = ['amount', 'currency', 'receipt'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit();
        }
    }

    // Validate amount (must be positive integer in paise)
    $amount = intval($input['amount']);
    if ($amount <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Amount must be a positive integer in paise']);
        exit();
    }

    // Validate currency
    if ($input['currency'] !== 'INR') {
        http_response_code(400);
        echo json_encode(['error' => 'Only INR currency is supported']);
        exit();
    }

    // Initialize Razorpay API
    $api = new Razorpay\Api\Api(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET);

    // Prepare order data
    $order_data = [
        'receipt' => $input['receipt'],
        'amount' => $amount,
        'currency' => $input['currency'],
        'notes' => array_merge([
            'created_by' => 'oneapp_flutter',
            'user_id' => $auth_result['user_id'],
            'timestamp' => date('Y-m-d H:i:s'),
            'client_type' => $_SERVER['HTTP_X_MCLIENT'] ?? 'unknown'
        ], $input['notes'] ?? [])
    ];

    // Create order through Razorpay API
    $razorpay_order = $api->order->create($order_data);

    // Store order in database for tracking
    $stmt = $pdo->prepare("
        INSERT INTO razorpay_orders 
        (order_id, receipt, amount, currency, status, user_id, notes, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $razorpay_order['id'],
        $input['receipt'],
        $amount,
        $input['currency'],
        $razorpay_order['status'],
        $auth_result['user_id'],
        json_encode($order_data['notes'])
    ]);

    // Log successful order creation
    Logger::log('INFO', 'Razorpay order created', [
        'order_id' => $razorpay_order['id'],
        'amount' => $amount,
        'user_id' => $auth_result['user_id'],
        'receipt' => $input['receipt']
    ]);

    // Return order details
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'order_id' => $razorpay_order['id'],
        'amount' => $razorpay_order['amount'],
        'currency' => $razorpay_order['currency'],
        'receipt' => $razorpay_order['receipt'],
        'status' => $razorpay_order['status'],
        'created_at' => $razorpay_order['created_at'],
        'notes' => $razorpay_order['notes']
    ]);

} catch (Razorpay\Api\Errors\BadRequestError $e) {
    Logger::log('ERROR', 'Razorpay order creation failed - Bad Request', [
        'error' => $e->getMessage(),
        'user_id' => $auth_result['user_id'] ?? 'unknown'
    ]);
    
    http_response_code(400);
    echo json_encode([
        'error' => 'Invalid request to Razorpay',
        'details' => $e->getMessage()
    ]);

} catch (Razorpay\Api\Errors\ServerError $e) {
    Logger::log('ERROR', 'Razorpay server error', [
        'error' => $e->getMessage(),
        'user_id' => $auth_result['user_id'] ?? 'unknown'
    ]);
    
    http_response_code(502);
    echo json_encode([
        'error' => 'Razorpay service unavailable',
        'details' => 'Please try again later'
    ]);

} catch (Exception $e) {
    Logger::log('ERROR', 'Order creation failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'user_id' => $auth_result['user_id'] ?? 'unknown'
    ]);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'details' => 'Order creation failed'
    ]);
}
?>
