#!/bin/bash

# Razorpay Integration Test Runner
# This script runs comprehensive tests for the Razorpay integration following SSO-Flutter patterns

set -e

echo "🚀 Starting Razorpay Integration Tests..."
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Flutter version:"
flutter --version

# Clean and get dependencies
print_status "Cleaning project and getting dependencies..."
flutter clean
flutter pub get

# Generate mock files
print_status "Generating mock files for tests..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run unit tests
print_status "Running Razorpay Service unit tests..."
flutter test test/unit/razorpay_service_test.dart --coverage

if [ $? -eq 0 ]; then
    print_success "Unit tests passed!"
else
    print_error "Unit tests failed!"
    exit 1
fi

# Run integration tests
print_status "Running Razorpay integration tests..."
flutter test test/integration/razorpay_integration_test.dart --coverage

if [ $? -eq 0 ]; then
    print_success "Integration tests passed!"
else
    print_error "Integration tests failed!"
    exit 1
fi

# Run all tests together
print_status "Running complete test suite..."
flutter test --coverage

if [ $? -eq 0 ]; then
    print_success "All tests passed!"
else
    print_error "Some tests failed!"
    exit 1
fi

# Generate coverage report
print_status "Generating coverage report..."
if command -v lcov &> /dev/null; then
    lcov --remove coverage/lcov.info \
        'lib/generated/*' \
        'lib/l10n/*' \
        'lib/**/*.g.dart' \
        'lib/**/*.freezed.dart' \
        'test/*' \
        -o coverage/lcov_cleaned.info
    
    genhtml coverage/lcov_cleaned.info -o coverage/html
    print_success "Coverage report generated in coverage/html/"
else
    print_warning "lcov not installed. Install with: brew install lcov (macOS) or apt-get install lcov (Ubuntu)"
fi

# Test specific scenarios
print_status "Running specific Razorpay test scenarios..."

echo ""
echo "🧪 Test Scenarios:"
echo "=================="

# Test 1: Order Creation
print_status "Testing order creation flow..."
flutter test test/integration/razorpay_integration_test.dart --name "should create order using SSO-Flutter maintenance service"

# Test 2: Payment Verification
print_status "Testing payment verification flow..."
flutter test test/integration/razorpay_integration_test.dart --name "should verify payment signature successfully"

# Test 3: Payment Completion
print_status "Testing payment completion flow..."
flutter test test/integration/razorpay_integration_test.dart --name "should complete payment using SSO-Flutter service"

# Test 4: Error Handling
print_status "Testing error handling..."
flutter test test/integration/razorpay_integration_test.dart --name "should handle network timeouts gracefully"

# Test 5: Environment Configuration
print_status "Testing environment configuration..."
flutter test test/integration/razorpay_integration_test.dart --name "should use correct endpoints for development environment"

print_success "All specific test scenarios completed!"

echo ""
echo "📊 Test Summary:"
echo "================"
echo "✅ Unit Tests: Passed"
echo "✅ Integration Tests: Passed"
echo "✅ Order Creation: Tested"
echo "✅ Payment Verification: Tested"
echo "✅ Payment Completion: Tested"
echo "✅ Error Handling: Tested"
echo "✅ Environment Config: Tested"

echo ""
print_success "🎉 All Razorpay integration tests completed successfully!"
print_status "The Razorpay integration is ready for production deployment."

echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Update production Razorpay keys in lib/config/environment/production.dart"
echo "2. Configure webhook endpoints in Razorpay dashboard"
echo "3. Test with real payment methods in staging environment"
echo "4. Deploy to production"

echo ""
print_status "Test run completed at $(date)"
