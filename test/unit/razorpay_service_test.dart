import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';

import '../../lib/core/services/razorpay_service.dart';
import '../../lib/config/environment/environment.dart';

// Generate mocks
@GenerateMocks([Razorpay])
import 'razorpay_service_test.mocks.dart';

void main() {
  group('RazorpayService Unit Tests', () {
    late MockRazorpay mockRazorpay;
    late RazorpayService razorpayService;

    setUp(() {
      mockRazorpay = MockRazorpay();
      razorpayService = RazorpayService.instance;
      
      // Set test environment
      Environment.setEnvironment(EnvironmentType.development);
    });

    group('Service Initialization', () {
      test('should initialize Razorpay service successfully', () async {
        // Act
        await razorpayService.initialize();

        // Assert
        expect(razorpayService.isInitialized, isTrue);
      });

      test('should be singleton instance', () {
        // Act
        final instance1 = RazorpayService.instance;
        final instance2 = RazorpayService.instance;

        // Assert
        expect(instance1, same(instance2));
      });
    });

    group('Callback Management', () {
      test('should set success and error callbacks', () {
        // Arrange
        bool successCalled = false;
        bool errorCalled = false;

        void onSuccess(PaymentSuccessResponse response) {
          successCalled = true;
        }

        void onError(PaymentFailureResponse response) {
          errorCalled = true;
        }

        // Act
        razorpayService.setCallbacks(
          onSuccess: onSuccess,
          onError: onError,
        );

        // Simulate callbacks
        razorpayService.handlePaymentSuccess(PaymentSuccessResponse(
          'pay_test_123',
          'order_test_123',
          'test_signature',
        ));

        razorpayService.handlePaymentError(PaymentFailureResponse(
          1,
          'Test error',
          {},
        ));

        // Assert
        expect(successCalled, isTrue);
        expect(errorCalled, isTrue);
      });
    });

    group('Payment Options Generation', () {
      test('should generate correct payment options for maintenance payment', () {
        // Arrange
        const amount = 1000.0;
        const accountName = 'Test Society';
        const orderId = 'order_test_123';

        // Act
        final options = razorpayService.generatePaymentOptions(
          amount: amount,
          orderId: orderId,
          description: 'Maintenance Payment',
          prefill: {
            'name': accountName,
            'email': '<EMAIL>',
            'contact': '**********',
          },
        );

        // Assert
        expect(options['key'], equals(Environment.config.razorpayKey));
        expect(options['amount'], equals((amount * 100).toInt()));
        expect(options['order_id'], equals(orderId));
        expect(options['description'], equals('Maintenance Payment'));
        expect(options['prefill']['name'], equals(accountName));
      });

      test('should handle missing prefill data gracefully', () {
        // Arrange
        const amount = 500.0;
        const orderId = 'order_test_456';

        // Act
        final options = razorpayService.generatePaymentOptions(
          amount: amount,
          orderId: orderId,
          description: 'Test Payment',
        );

        // Assert
        expect(options['key'], equals(Environment.config.razorpayKey));
        expect(options['amount'], equals((amount * 100).toInt()));
        expect(options['order_id'], equals(orderId));
        expect(options['prefill'], isNull);
      });
    });

    group('Amount Validation', () {
      test('should validate positive amounts', () {
        // Act & Assert
        expect(() => razorpayService.validateAmount(100.0), returnsNormally);
        expect(() => razorpayService.validateAmount(0.01), returnsNormally);
        expect(() => razorpayService.validateAmount(99999.99), returnsNormally);
      });

      test('should reject negative amounts', () {
        // Act & Assert
        expect(() => razorpayService.validateAmount(-1.0), throwsArgumentError);
        expect(() => razorpayService.validateAmount(-100.0), throwsArgumentError);
      });

      test('should reject zero amount', () {
        // Act & Assert
        expect(() => razorpayService.validateAmount(0.0), throwsArgumentError);
      });

      test('should reject extremely large amounts', () {
        // Act & Assert
        expect(() => razorpayService.validateAmount(100000.0), throwsArgumentError);
      });
    });

    group('Order ID Validation', () {
      test('should validate proper order IDs', () {
        // Act & Assert
        expect(() => razorpayService.validateOrderId('order_test_123'), returnsNormally);
        expect(() => razorpayService.validateOrderId('order_ABC123'), returnsNormally);
      });

      test('should reject empty order IDs', () {
        // Act & Assert
        expect(() => razorpayService.validateOrderId(''), throwsArgumentError);
        expect(() => razorpayService.validateOrderId('   '), throwsArgumentError);
      });

      test('should reject invalid order ID formats', () {
        // Act & Assert
        expect(() => razorpayService.validateOrderId('invalid_format'), throwsArgumentError);
        expect(() => razorpayService.validateOrderId('123'), throwsArgumentError);
      });
    });

    group('Error Handling', () {
      test('should handle payment success response correctly', () {
        // Arrange
        bool callbackCalled = false;
        PaymentSuccessResponse? receivedResponse;

        razorpayService.setCallbacks(
          onSuccess: (response) {
            callbackCalled = true;
            receivedResponse = response;
          },
          onError: (response) {},
        );

        final testResponse = PaymentSuccessResponse(
          'pay_test_123',
          'order_test_123',
          'test_signature',
        );

        // Act
        razorpayService.handlePaymentSuccess(testResponse);

        // Assert
        expect(callbackCalled, isTrue);
        expect(receivedResponse?.paymentId, equals('pay_test_123'));
        expect(receivedResponse?.orderId, equals('order_test_123'));
        expect(receivedResponse?.signature, equals('test_signature'));
      });

      test('should handle payment error response correctly', () {
        // Arrange
        bool callbackCalled = false;
        PaymentFailureResponse? receivedResponse;

        razorpayService.setCallbacks(
          onSuccess: (response) {},
          onError: (response) {
            callbackCalled = true;
            receivedResponse = response;
          },
        );

        final testResponse = PaymentFailureResponse(
          1001,
          'Payment failed',
          {'reason': 'insufficient_funds'},
        );

        // Act
        razorpayService.handlePaymentError(testResponse);

        // Assert
        expect(callbackCalled, isTrue);
        expect(receivedResponse?.code, equals(1001));
        expect(receivedResponse?.message, equals('Payment failed'));
      });

      test('should handle external wallet response correctly', () {
        // Arrange
        bool callbackCalled = false;
        String? receivedWalletName;

        razorpayService.setCallbacks(
          onSuccess: (response) {},
          onError: (response) {},
          onExternalWallet: (response) {
            callbackCalled = true;
            receivedWalletName = response.walletName;
          },
        );

        final testResponse = ExternalWalletResponse('paytm');

        // Act
        razorpayService.handleExternalWallet(testResponse);

        // Assert
        expect(callbackCalled, isTrue);
        expect(receivedWalletName, equals('paytm'));
      });
    });

    group('Service State Management', () {
      test('should track initialization state correctly', () {
        // Arrange
        final service = RazorpayService.instance;

        // Act & Assert
        expect(service.isInitialized, isFalse);
        
        service.initialize();
        expect(service.isInitialized, isTrue);
      });

      test('should allow reinitialization', () async {
        // Arrange
        final service = RazorpayService.instance;

        // Act
        await service.initialize();
        expect(service.isInitialized, isTrue);

        await service.initialize(); // Reinitialize
        
        // Assert
        expect(service.isInitialized, isTrue);
      });
    });

    group('Configuration Tests', () {
      test('should use development configuration in test environment', () {
        // Arrange
        Environment.setEnvironment(EnvironmentType.development);

        // Act
        final config = Environment.config;

        // Assert
        expect(config.razorpayKey, startsWith('rzp_test_'));
        expect(config.environment, equals('development'));
        expect(config.enableLogging, isTrue);
      });

      test('should use production configuration when set', () {
        // Arrange
        Environment.setEnvironment(EnvironmentType.production);

        // Act
        final config = Environment.config;

        // Assert
        expect(config.environment, equals('production'));
        expect(config.enableLogging, isFalse);
      });
    });
  });
}
