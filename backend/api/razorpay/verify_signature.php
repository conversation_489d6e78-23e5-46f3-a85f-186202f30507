<?php
/**
 * Razorpay Payment Signature Verification Endpoint
 * Following SSO-Flutter security patterns
 * 
 * Endpoint: POST /api/v1/verifyRazorpaySignature
 * Headers: x-access-token, x-api-token, X-MClient
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, x-access-token, x-api-token, X-MClient');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

require_once '../config/database.php';
require_once '../config/razorpay_config.php';
require_once '../utils/auth_validator.php';
require_once '../utils/logger.php';
require_once '../utils/signature_verifier.php';

try {
    // Validate authentication headers (SSO-Flutter pattern)
    $auth_result = AuthValidator::validateRequest();
    if (!$auth_result['valid']) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication failed', 'details' => $auth_result['message']]);
        exit();
    }

    // Get request body
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required_fields = ['razorpay_payment_id', 'razorpay_order_id', 'razorpay_signature'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit();
        }
    }

    $payment_id = $input['razorpay_payment_id'];
    $order_id = $input['razorpay_order_id'];
    $signature = $input['razorpay_signature'];

    // Verify the order exists in our database
    $stmt = $pdo->prepare("SELECT * FROM razorpay_orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        Logger::log('WARNING', 'Signature verification attempted for unknown order', [
            'order_id' => $order_id,
            'payment_id' => $payment_id,
            'user_id' => $auth_result['user_id']
        ]);
        
        http_response_code(404);
        echo json_encode([
            'error' => 'Order not found',
            'signature_valid' => false
        ]);
        exit();
    }

    // Verify signature using Razorpay's method
    $signature_valid = SignatureVerifier::verifyPaymentSignature(
        $payment_id,
        $order_id,
        $signature,
        RAZORPAY_KEY_SECRET
    );

    // Additional security: Verify payment details with Razorpay API
    $payment_details = null;
    $api_verification = false;
    
    try {
        $api = new Razorpay\Api\Api(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET);
        $payment = $api->payment->fetch($payment_id);
        
        // Verify payment belongs to the order and is captured
        $api_verification = (
            $payment['order_id'] === $order_id &&
            $payment['status'] === 'captured' &&
            $payment['amount'] === $order['amount']
        );
        
        $payment_details = [
            'status' => $payment['status'],
            'amount' => $payment['amount'],
            'method' => $payment['method'],
            'captured' => $payment['captured'],
            'created_at' => $payment['created_at']
        ];
        
    } catch (Exception $e) {
        Logger::log('WARNING', 'Failed to fetch payment details from Razorpay API', [
            'payment_id' => $payment_id,
            'error' => $e->getMessage()
        ]);
    }

    // Final verification result
    $verification_result = $signature_valid && $api_verification;

    // Store verification result
    $stmt = $pdo->prepare("
        INSERT INTO payment_verifications 
        (payment_id, order_id, signature, signature_valid, api_verification, user_id, verification_timestamp, payment_details) 
        VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)
    ");
    
    $stmt->execute([
        $payment_id,
        $order_id,
        $signature,
        $signature_valid ? 1 : 0,
        $api_verification ? 1 : 0,
        $auth_result['user_id'],
        json_encode($payment_details)
    ]);

    // Update order status if verification successful
    if ($verification_result) {
        $stmt = $pdo->prepare("
            UPDATE razorpay_orders 
            SET status = 'paid', payment_id = ?, verified_at = NOW() 
            WHERE order_id = ?
        ");
        $stmt->execute([$payment_id, $order_id]);
    }

    // Log verification result
    Logger::log($verification_result ? 'INFO' : 'WARNING', 'Payment signature verification', [
        'payment_id' => $payment_id,
        'order_id' => $order_id,
        'signature_valid' => $signature_valid,
        'api_verification' => $api_verification,
        'final_result' => $verification_result,
        'user_id' => $auth_result['user_id']
    ]);

    // Return verification result
    http_response_code(200);
    echo json_encode([
        'signature_valid' => $verification_result,
        'verification_details' => [
            'signature_check' => $signature_valid,
            'api_verification' => $api_verification,
            'payment_status' => $payment_details['status'] ?? 'unknown',
            'verification_timestamp' => date('Y-m-d H:i:s')
        ],
        'payment_details' => $payment_details
    ]);

} catch (Exception $e) {
    Logger::log('ERROR', 'Signature verification failed', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'payment_id' => $input['razorpay_payment_id'] ?? 'unknown',
        'order_id' => $input['razorpay_order_id'] ?? 'unknown',
        'user_id' => $auth_result['user_id'] ?? 'unknown'
    ]);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Verification failed',
        'signature_valid' => false,
        'details' => 'Internal server error during verification'
    ]);
}
?>
