abstract class BaseConfig {
  // Primary SSO Configuration
  String get ssoAuthUrl;
  String get ssoClientId;
  String get ssoClientSecret;
  String get ssoApiKey;
  
  // OnePay Configuration
  String get onePayBaseUrl;
  String get onePayKeycloakUrl;
  String get onePayConfigUrl;
  
  // Payment Gateway
  String get razorpayKey;
  String get razorpayWebhookSecret;
  String get chsoneOperatorsUrl;
  String get apiToken;
  
  // Service URLs
  String get rechargeApiUrl;
  String get maintenanceApiUrl;
  String get billPaymentApiUrl;
  
  // App Configuration
  String get environment;
  bool get enableLogging;
  
  // Service Definitions
  List<Map<String, dynamic>> get services;
} 