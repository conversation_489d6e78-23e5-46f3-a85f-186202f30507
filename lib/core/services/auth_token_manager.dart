import 'dart:developer';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'keycloak_service.dart';
import 'chsone_auth_service.dart';
import '../../utils/storage/sso_storage.dart';
import '../../utils/storage/auth_integration_service.dart';

/// Unified Authentication Token Manager
/// Manages token priority, refresh, and validation across all authentication sources
class AuthTokenManager {
  static const String _logName = 'AuthTokenManager';

  /// Get the best available token based on priority system
  /// Priority: 1. Keycloak, 2. CHSONE, 3. SSO Storage
  static Future<String?> getBestAvailableToken() async {
    try {
      // Priority 1: Keycloak (Primary authentication)
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null && await _isTokenValid(keycloakToken)) {
        log('Using Keycloak token', name: _logName);
        return keycloakToken;
      }

      // Priority 2: CHSONE (Society-specific)
      final chsoneToken =
          await ChsoneAuthService.instance.getChsoneAccessToken();
      if (chsoneToken != null && await _isTokenValid(chsoneToken)) {
        log('Using CHSONE token', name: _logName);
        return chsoneToken;
      }

      // Priority 3: SSO Storage (Fallback)
      final ssoToken = await SsoStorage.getAccessToken();
      if (ssoToken != null && await _isTokenValid(ssoToken)) {
        log('Using SSO Storage token', name: _logName);
        return ssoToken;
      }

      log('No valid token available', name: _logName);
      return null;
    } catch (e) {
      log('Error getting token: $e', name: _logName);
      return null;
    }
  }

  /// Check if token is valid and not expired
  static Future<bool> _isTokenValid(String token) async {
    try {
      if (token.isEmpty) return false;

      // Check if token is expired
      if (JwtDecoder.isExpired(token)) {
        return false;
      }

      // Additional validation can be added here
      return true;
    } catch (e) {
      log('Token validation error: $e', name: _logName);
      return false;
    }
  }

  /// Check if token is expiring soon (within 5 minutes)
  static bool _isTokenExpiringSoon(String token) {
    try {
      final decodedToken = JwtDecoder.decode(token);
      final expiryDate =
          DateTime.fromMillisecondsSinceEpoch(decodedToken['exp'] * 1000);
      final now = DateTime.now();
      final timeUntilExpiry = expiryDate.difference(now);

      // Refresh if token expires within 5 minutes
      return timeUntilExpiry.inMinutes <= 5;
    } catch (e) {
      log('Token expiry check error: $e', name: _logName);
      return true; // Assume expired if can't decode
    }
  }

  /// Refresh tokens if needed
  static Future<bool> refreshTokenIfNeeded() async {
    try {
      bool refreshed = false;

      // Check Keycloak token validity
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null && _isTokenExpiringSoon(keycloakToken)) {
        log('Keycloak token expiring soon, checking validity', name: _logName);
        final isValid = await KeycloakService.isTokenValid();
        if (!isValid) {
          log('Keycloak token invalid, clearing tokens', name: _logName);
          await KeycloakService.clearTokens();
        }
      }

      // Check CHSONE token validity
      final chsoneToken =
          await ChsoneAuthService.instance.getChsoneAccessToken();
      if (chsoneToken != null && _isTokenExpiringSoon(chsoneToken)) {
        log('CHSONE token expiring soon, checking authentication',
            name: _logName);
        final isAuth = await ChsoneAuthService.instance.isChsoneAuthenticated();
        if (!isAuth) {
          log('CHSONE authentication invalid', name: _logName);
          refreshed =
              true; // Mark as refreshed since CHSONE handles its own refresh
        }
      }

      return refreshed;
    } catch (e) {
      log('Token refresh check failed: $e', name: _logName);
      return false;
    }
  }

  /// Force refresh all tokens
  static Future<bool> forceRefreshAllTokens() async {
    try {
      bool anyRefreshed = false;

      // Force check Keycloak authentication
      try {
        final isKeycloakAuth = await KeycloakService.isAuthenticated();
        if (!isKeycloakAuth) {
          log('Keycloak not authenticated, clearing tokens', name: _logName);
          await KeycloakService.clearTokens();
          anyRefreshed = true;
        }
      } catch (e) {
        log('Keycloak force check failed: $e', name: _logName);
      }

      // Force check CHSONE authentication
      try {
        final isChsoneAuth =
            await ChsoneAuthService.instance.isChsoneAuthenticated();
        if (!isChsoneAuth) {
          log('CHSONE not authenticated', name: _logName);
          anyRefreshed = true;
        }
      } catch (e) {
        log('CHSONE force check failed: $e', name: _logName);
      }

      return anyRefreshed;
    } catch (e) {
      log('Force refresh all tokens failed: $e', name: _logName);
      return false;
    }
  }

  /// Get user data from the best available source
  static Future<Map<String, dynamic>?> getUserData() async {
    try {
      // Try Keycloak first
      final keycloakData = await KeycloakService.getUserData();
      if (keycloakData != null && keycloakData.isNotEmpty) {
        return keycloakData;
      }

      // Try CHSONE
      final chsoneData = await ChsoneAuthService.instance.getStoredAuthData();
      if (chsoneData != null && chsoneData.isNotEmpty) {
        return chsoneData;
      }

      // Try AuthIntegrationService
      final integrationData = await AuthIntegrationService.getUserProfile();
      if (integrationData != null && integrationData.isNotEmpty) {
        return integrationData;
      }

      return null;
    } catch (e) {
      log('Error getting user data: $e', name: _logName);
      return null;
    }
  }

  /// Get authentication headers with best available token
  static Future<Map<String, String>> getAuthHeaders() async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add authentication token
    final token = await getBestAvailableToken();
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    // Add user context headers
    final userData = await getUserData();
    if (userData != null) {
      if (userData['user_id'] != null) {
        headers['X-User-ID'] = userData['user_id'].toString();
      }
      if (userData['soc_id'] != null) {
        headers['X-Society-ID'] = userData['soc_id'].toString();
      }
      if (userData['unit_id'] != null) {
        headers['X-Unit-ID'] = userData['unit_id'].toString();
      }
      if (userData['member_id'] != null) {
        headers['X-Member-ID'] = userData['member_id'].toString();
      }
    }

    return headers;
  }

  /// Clear all tokens (for logout)
  static Future<void> clearAllTokens() async {
    try {
      await KeycloakService.logout();
      await ChsoneAuthService.instance.signOut();
      // Clear SSO storage (implement clearAll method if needed)
      // await SsoStorage.clearAll();
      // Clear integration tokens (implement clearAllTokens method if needed)
      // await AuthIntegrationService.clearAllTokens();

      // Clear all authentication data
      await SsoStorage.clearAuthData();
      log('All tokens cleared', name: _logName);
    } catch (e) {
      log('Error clearing tokens: $e', name: _logName);
    }
  }

  /// Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final token = await getBestAvailableToken();
    return token != null;
  }

  /// Get token expiry information
  static Future<DateTime?> getTokenExpiry() async {
    try {
      final token = await getBestAvailableToken();
      if (token == null) return null;

      final decodedToken = JwtDecoder.decode(token);
      return DateTime.fromMillisecondsSinceEpoch(decodedToken['exp'] * 1000);
    } catch (e) {
      log('Error getting token expiry: $e', name: _logName);
      return null;
    }
  }

  /// Get token information for debugging
  static Future<Map<String, dynamic>> getTokenInfo() async {
    try {
      final keycloakToken = await KeycloakService.getAccessToken();
      final chsoneToken =
          await ChsoneAuthService.instance.getChsoneAccessToken();
      final ssoToken = await SsoStorage.getAccessToken();
      final bestToken = await getBestAvailableToken();

      return {
        'keycloak_available': keycloakToken != null,
        'keycloak_valid':
            keycloakToken != null ? await _isTokenValid(keycloakToken) : false,
        'chsone_available': chsoneToken != null,
        'chsone_valid':
            chsoneToken != null ? await _isTokenValid(chsoneToken) : false,
        'sso_available': ssoToken != null,
        'sso_valid': ssoToken != null ? await _isTokenValid(ssoToken) : false,
        'best_token_available': bestToken != null,
        'token_expiry': await getTokenExpiry(),
        'is_authenticated': await isAuthenticated(),
      };
    } catch (e) {
      log('Error getting token info: $e', name: _logName);
      return {'error': e.toString()};
    }
  }
}
