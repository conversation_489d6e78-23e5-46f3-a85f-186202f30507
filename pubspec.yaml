name: oneapp
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_animate: ^4.5.0
  go_router: ^14.8.1
  flutter_riverpod: ^2.4.0
  google_fonts: ^6.2.0
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.10+1
  flutter_carousel_widget: ^2.2.0
  supabase_flutter: ^2.3.3
  flutter_dotenv: ^5.1.0
  keycloak_wrapper: ^1.1.5
  flutter_secure_storage: ^9.0.0
  http: ^1.2.0
  jwt_decoder: ^2.0.1
  dart_jsonwebtoken: ^2.12.1
  crypto: ^3.0.3
  device_preview: ^1.2.0
  image_picker: ^1.0.7
  shared_preferences: ^2.2.2
  url_launcher: ^6.3.1
  qr_flutter: ^4.1.0
  share_plus: ^7.2.2
  path_provider: ^2.1.2
  intl: ^0.20.2
  permission_handler: ^11.0.1
  flutter_contacts: ^1.1.9+2
  uuid: ^4.5.1
  timeago: ^3.6.0
  badges: ^3.1.2
  video_player: ^2.8.3
  chewie: ^1.7.5
  equatable: ^2.0.5

  # OneApp dependencies (merged from oneapp_flutter)
  dio: ^5.7.0
  pretty_dio_logger: ^1.4.0
  flutter_html: ^3.0.0-beta.2
  webview_flutter: ^4.5.0
  validator_regex: ^1.1.1
  sms_autofill: ^2.4.1
  connectivity_plus: ^5.0.2
  speech_to_text: ^7.0.0
  mailto: ^2.0.0
  flutter_local_notifications: ^17.2.4

  # OneApp UI dependencies
  flutter_screenutil: ^5.9.3
  coupon_uikit: ^0.2.1
  flutter_spinkit: ^5.2.1
  carousel_slider: ^5.0.0
  shimmer: ^3.0.0
  avatar_glow: ^3.0.1
  table_calendar: ^3.0.9
  pin_code_fields: ^8.0.1
  country_picker: ^2.0.21
  fluttertoast: ^8.2.5

  # OneApp device info
  dart_ipify: ^1.1.1
  device_info_plus: ^9.1.2
  package_info_plus: ^4.2.0

  # OneApp location services
  geolocator: ^11.1.0
  geocoding: ^3.0.0
  google_maps_flutter: ^2.5.3
  google_maps_flutter_web: ^0.5.1
  google_places_flutter: ^2.0.9

  # OneApp BLoC dependencies
  flutter_bloc: ^8.1.6
  hydrated_bloc: ^9.1.5

  # OneApp Firebase (some already exist, adding missing ones)
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  firebase_crashlytics: ^3.3.4
  firebase_analytics: ^10.4.4
  firebase_dynamic_links: ^5.4.17

  # OneApp third party
  google_sign_in: ^6.1.6
  flutter_facebook_auth: ^6.0.4
  flutter_dynamic_icon: ^2.1.0

  # OneApp payment
  razorpay_flutter: ^1.3.5
  flutter_web_frame: ^1.0.0

  # OneApp localization
  flutter_localizations:
    sdk: flutter

  # OneApp additional
  app_links: ^3.5.1
  app_tracking_transparency: ^2.0.6+1
  confetti: ^0.8.0

  # Existing dependencies
  json_annotation: ^4.9.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.5
  mcp_toolkit: ^0.1.2
  
  # Freezed for state management
  freezed_annotation: ^2.4.4
  
  # Dependency Injection
  get_it: ^7.6.4
  injectable: ^2.3.2
  
  # State Management
  provider: ^6.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

  # Testing dependencies
  mockito: ^5.4.4
  build_runner: ^2.4.7

  # Code generation dependencies
  freezed: ^2.5.7
  injectable_generator: ^2.4.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # Screens directory for NeighbourScreen
    - lib/screens/

    # Background Images
    - assets/images/homeFooterBg.jpg
    - assets/images/

    # Carousel Icon Images
    - assets/images/carousel/

    # Icons directory
    - assets/icons/

    # videos
    - assets/videos/
    - assets/videos/gatekeeper.mp4
    - assets/videos/resident.mp4
    - assets/videos/meeting.mp4
    - assets/videos/announcement.mp4

    # OneApp assets (merged from oneapp_flutter)
    - assets/images/oneapp/
    - assets/images/location_container.png
    - assets/images/delete_forever.png
    - assets/images/calendar_month.png
    - assets/images/alarm.png

    # Environment configuration (if loading from assets instead of root)
    # - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts (merged from OneApp)
  fonts:
    - family: Signika
      fonts:
        - asset: assets/fonts/Signika-Bold.ttf
        - asset: assets/fonts/Signika-Light.ttf
        - asset: assets/fonts/Signika-Medium.ttf
        - asset: assets/fonts/Signika-Regular.ttf
        - asset: assets/fonts/Signika-SemiBold.ttf

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter internationalization (from OneApp)
flutter_intl:
  enabled: true

# Dependency overrides to resolve conflicts
dependency_overrides:
  http: ^1.2.0
  flutter_dynamic_icon:
    path: ./packages/flutter_dynamic_icon
  image_cropper:
    path: ./packages/image_cropper
  image_cropper_platform_interface: ^3.0.3
  image_cropper_for_web: ^1.0.3
  geolocator_android: ^5.0.1
  permission_handler: ^12.0.0
  permission_handler_android: ^12.0.0
  permission_handler_platform_interface: ^4.0.0
  speech_to_text: ^7.0.0
  win32: ^5.0.0
  flutter_secure_storage_windows: ^2.0.0
  win32_registry: ^1.1.1
  # OneApp dependency overrides
  flutter_secure_storage: ^8.0.0
  firebase_core_platform_interface: 5.0.0
  firebase_messaging_platform_interface: 4.5.1
  firebase_messaging_web: 3.5.1
