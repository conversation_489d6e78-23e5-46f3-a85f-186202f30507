import 'base_config.dart';

class ProductionConfig extends BaseConfig {
  @override
  String get ssoAuthUrl => "https://authapi.chsone.in/api/v2/";
  
  @override
  String get ssoClientId => "web_client";
  
  @override
  String get ssoClientSecret => "ZMsRmR2vXH5Q2one";
  
  @override
  String get ssoApiKey => "5b2f4b31f524b6603d81dbcaabd8121078d00f1614a294c3207e39951f7054c8";
  
  @override
  String get onePayBaseUrl => "https://apigw.cubeone.in/onepay-live/api/v2/";
  
  @override
  String get onePayKeycloakUrl => "https://onepay.cubeone.in/api/v1/dev/login";
  
  @override
  String get onePayConfigUrl => "https://fstech-cms-db.s3.ap-south-1.amazonaws.com/onepay_key_f3b4aea81d.json";
  
  @override
  String get razorpayKey => "rzp_live_YOUR_LIVE_KEY_HERE"; // TODO: Replace with production Razorpay key

  @override
  String get razorpayWebhookSecret => "whsec_YOUR_WEBHOOK_SECRET_HERE"; // TODO: Replace with production webhook secret

  @override
  String get chsoneOperatorsUrl => "https://chsone.in/api/v1/operators/"; // Production CHSOne operators URL

  @override
  String get apiToken => "YOUR_PRODUCTION_API_TOKEN_HERE"; // TODO: Replace with production API token // Using current test key - update with production key when deploying
  
  @override
  String get rechargeApiUrl => "${onePayBaseUrl}recharge/";
  
  @override
  String get maintenanceApiUrl => "https://authapi.chsone.in/api/v2/";
  
  @override
  String get billPaymentApiUrl => "${onePayBaseUrl}bills/";
  
  @override
  String get environment => "production";
  
  @override
  bool get enableLogging => false;
  
  @override
  List<Map<String, dynamic>> get services => [
    {
      'id': 'mobile_recharge',
      'name': 'Mobile Recharge',
      'type': 'prepaid',
      'icon': 'smartphone',
      'active': true,
      'endpoint': '${rechargeApiUrl}mobile',
    },
    {
      'id': 'maintenance_payment',
      'name': 'Maintenance Payment',
      'type': 'payment',
      'icon': 'home_repair_service',
      'active': true,
      'endpoint': '${maintenanceApiUrl}payment',
    },
    {
      'id': 'utility_bills',
      'name': 'Utility Bills',
      'type': 'bills',
      'icon': 'receipt_long',
      'active': true,
      'endpoint': '${billPaymentApiUrl}utility',
    },
    {
      'id': 'dth_recharge',
      'name': 'DTH Recharge',
      'type': 'dth',
      'icon': 'tv',
      'active': true,
      'endpoint': '${rechargeApiUrl}dth',
    },
  ];
} 