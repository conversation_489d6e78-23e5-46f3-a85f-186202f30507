import 'base_config.dart';

class DevelopmentConfig extends BaseConfig {
  @override
  String get ssoAuthUrl => "https://sso.cubeone.in/api/v1/";
  
  @override
  String get ssoClientId => "cubeone_client_dev";
  
  @override
  String get ssoClientSecret => "dev_client_secret";
  
  @override
  String get ssoApiKey => "dev_api_key_123";
  
  @override
  String get onePayBaseUrl => "https://apigw.cubeone.in/onepay-dev/api/v2/";
  
  @override
  String get onePayKeycloakUrl => "https://keycloak.cubeone.in/auth/realms/onepay-dev/";
  
  @override
  String get onePayConfigUrl => "https://apigw.cubeone.in/onepay-dev/api/v2/config";
  
  @override
  String get razorpayKey => "rzp_test_DCmn2Xc0a7coSH"; // Test key for development
  
  @override
  String get razorpayWebhookSecret => "webhook_secret_test_123"; // Test webhook secret
  
  @override
  String get chsoneOperatorsUrl => "https://chsone.in/api/v1/operators/"; // Same as production for now
  
  @override
  String get apiToken => "dev_api_token_123"; // Development API token
  
  @override
  String get rechargeApiUrl => "https://api-dev.yourapp.com/recharge/";
  
  @override
  String get maintenanceApiUrl => "https://api-dev.yourapp.com/maintenance/";
  
  @override
  String get billPaymentApiUrl => "https://api-dev.yourapp.com/bills/";
  
  @override
  String get environment => "development";
  
  @override
  bool get enableLogging => true; // Enable detailed logging in development
  
  @override
  List<Map<String, dynamic>> get services => [
    {
      'id': 'recharge',
      'name': 'Mobile Recharge',
      'icon': 'phone',
      'enabled': true,
      'test_mode': true,
    },
    {
      'id': 'maintenance',
      'name': 'Maintenance Payment',
      'icon': 'home',
      'enabled': true,
      'test_mode': true,
    },
    {
      'id': 'bills',
      'name': 'Bill Payment',
      'icon': 'receipt',
      'enabled': true,
      'test_mode': true,
    },
    {
      'id': 'easylife',
      'name': 'EasyLife Services',
      'icon': 'services',
      'enabled': true,
      'test_mode': true,
    },
  ];
}
