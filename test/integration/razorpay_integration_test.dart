import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../lib/core/services/razorpay_service.dart';
import '../../lib/core/services/sso_flutter_maintenance_service.dart';
import '../../lib/core/services/razorpay_payment_verification_service.dart';
import '../../lib/config/environment/environment.dart';

// Generate mocks
@GenerateMocks([http.Client])
import 'razorpay_integration_test.mocks.dart';

void main() {
  group('Razorpay Integration Tests (SSO-Flutter Pattern)', () {
    late MockClient mockClient;
    late RazorpayService razorpayService;

    setUp(() {
      mockClient = MockClient();
      razorpayService = RazorpayService.instance;
      
      // Set test environment
      Environment.setEnvironment(EnvironmentType.development);
    });

    group('Order Creation Tests', () {
      test('should create order using SSO-Flutter maintenance service', () async {
        // Arrange
        final mockResponse = {
          'success': true,
          'data': {
            'order_id': 'order_test_123',
            'message': 'Order created successfully'
          }
        };

        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(mockResponse),
          200,
        ));

        // Act
        final result = await SSOFlutterMaintenanceService.initiateSocietyPayment(
          accountName: 'Test Account',
          totalPayableAmount: 1000.0,
          actualAmount: 1000.0,
          accountId: 'test_account_123',
          pan: '**********',
          note: 'Test payment',
        );

        // Assert
        expect(result['order_id'], equals('order_test_123'));
        expect(result['success'], isTrue);
      });

      test('should handle order creation failure gracefully', () async {
        // Arrange
        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode({'error': 'Invalid request'}),
          400,
        ));

        // Act & Assert
        expect(
          () => SSOFlutterMaintenanceService.initiateSocietyPayment(
            accountName: 'Test Account',
            totalPayableAmount: 1000.0,
            actualAmount: 1000.0,
            accountId: 'test_account_123',
          ),
          throwsException,
        );
      });
    });

    group('Payment Verification Tests', () {
      test('should verify payment signature successfully', () async {
        // Arrange
        const paymentId = 'pay_test_123';
        const orderId = 'order_test_123';
        const signature = 'test_signature_123';

        final mockResponse = {
          'signature_valid': true,
          'verification_details': {
            'signature_check': true,
            'api_verification': true,
            'payment_status': 'captured',
          }
        };

        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(mockResponse),
          200,
        ));

        // Act
        final isValid = await razorpayService.verifyPaymentSignature(
          paymentId: paymentId,
          orderId: orderId,
          signature: signature,
        );

        // Assert
        expect(isValid, isTrue);
      });

      test('should handle invalid signature verification', () async {
        // Arrange
        const paymentId = 'pay_test_123';
        const orderId = 'order_test_123';
        const signature = 'invalid_signature';

        final mockResponse = {
          'signature_valid': false,
          'verification_details': {
            'signature_check': false,
            'api_verification': false,
          }
        };

        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(mockResponse),
          200,
        ));

        // Act
        final isValid = await razorpayService.verifyPaymentSignature(
          paymentId: paymentId,
          orderId: orderId,
          signature: signature,
        );

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Payment Completion Tests', () {
      test('should complete payment using SSO-Flutter service', () async {
        // Arrange
        final mockResponse = {
          'success': true,
          'data': {
            'status': 'completed',
            'transaction_id': 'txn_test_123',
            'message': 'Payment completed successfully'
          }
        };

        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode(mockResponse),
          200,
        ));

        // Act
        final result = await SSOFlutterMaintenanceService.completeSocietyPayment(
          orderId: 'order_test_123',
          paymentId: 'pay_test_123',
          totalPayableAmount: 1000.0,
          accountId: 'test_account_123',
        );

        // Assert
        expect(result['success'], isTrue);
        expect(result['status'], equals('completed'));
      });

      test('should handle payment completion failure', () async {
        // Arrange
        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          json.encode({'error': 'Payment completion failed'}),
          500,
        ));

        // Act & Assert
        expect(
          () => SSOFlutterMaintenanceService.completeSocietyPayment(
            orderId: 'order_test_123',
            paymentId: 'pay_test_123',
            totalPayableAmount: 1000.0,
            accountId: 'test_account_123',
          ),
          throwsException,
        );
      });
    });

    group('Comprehensive Payment Validation Tests', () {
      test('should validate complete payment flow', () async {
        // Arrange
        const paymentId = 'pay_test_123';
        const orderId = 'order_test_123';
        const signature = 'test_signature_123';

        final mockValidationResponse = {
          'overall_status': 'valid',
          'validation_steps': {
            'signature_verification': {'status': 'passed'},
            'status_verification': {'status': 'completed'},
            'details_retrieval': {'status': 'completed'},
          }
        };

        when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
            .thenAnswer((_) async => http.Response(json.encode(mockValidationResponse), 200));

        when(mockClient.get(any, headers: anyNamed('headers')))
            .thenAnswer((_) async => http.Response(json.encode({'status': 'captured'}), 200));

        // Act
        final validationResult = await RazorpayPaymentVerificationService.validatePayment(
          paymentId: paymentId,
          orderId: orderId,
          signature: signature,
        );

        // Assert
        expect(validationResult['overall_status'], equals('valid'));
        expect(validationResult['validation_steps'], isNotNull);
      });
    });

    group('Error Handling Tests', () {
      test('should handle network timeouts gracefully', () async {
        // Arrange
        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenThrow(Exception('Network timeout'));

        // Act & Assert
        expect(
          () => SSOFlutterMaintenanceService.initiateSocietyPayment(
            accountName: 'Test Account',
            totalPayableAmount: 1000.0,
            actualAmount: 1000.0,
            accountId: 'test_account_123',
          ),
          throwsException,
        );
      });

      test('should handle malformed API responses', () async {
        // Arrange
        when(mockClient.post(
          any,
          headers: anyNamed('headers'),
          body: anyNamed('body'),
        )).thenAnswer((_) async => http.Response(
          'Invalid JSON response',
          200,
        ));

        // Act & Assert
        expect(
          () => SSOFlutterMaintenanceService.initiateSocietyPayment(
            accountName: 'Test Account',
            totalPayableAmount: 1000.0,
            actualAmount: 1000.0,
            accountId: 'test_account_123',
          ),
          throwsException,
        );
      });
    });

    group('Environment Configuration Tests', () {
      test('should use correct endpoints for development environment', () {
        // Arrange
        Environment.setEnvironment(EnvironmentType.development);

        // Act
        final config = Environment.config;

        // Assert
        expect(config.environment, equals('development'));
        expect(config.razorpayKey, startsWith('rzp_test_'));
        expect(config.enableLogging, isTrue);
      });

      test('should use correct endpoints for production environment', () {
        // Arrange
        Environment.setEnvironment(EnvironmentType.production);

        // Act
        final config = Environment.config;

        // Assert
        expect(config.environment, equals('production'));
        expect(config.enableLogging, isFalse);
      });
    });
  });
}
